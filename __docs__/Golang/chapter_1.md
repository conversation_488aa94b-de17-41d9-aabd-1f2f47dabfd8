# 

Lang in Depth
Guide to program
microservices, networking,
database and APIs using Go
Lang
<PERSON>
www.bpbonline.com
First Edition 2025
Copyright © BPB Publications, India
ISBN: 978-93-65894-806
All Rights Reserved. No part of this publication may be reproduced, distributed
or transmitted in any form or by any means or stored in a database or retrieval
system, without the prior written permission of the publisher with the exception
to the program listings which may be entered, stored and executed in a
computer system, but they can not be reproduced by the means of publication,
photocopy, recording, or by any electronic and mechanical means.


LIMITS OF LIABILITY AND DISCLAIMER OF WARRANTY
The information contained in this book is true to correct and the best of author’s
and publisher’s knowledge. The author has made every effort to ensure the
accuracy of these publications, but publisher cannot be held responsible for any
loss or damage arising from any information in this book.


All trademarks referred to in the book are acknowledged as properties of their
respective owners but BPB Publications cannot guarantee the accuracy of this
information.


www.bpbonline.com
Dedicated to
My dad who passed away during this book
writing and
My mom my strength and support system
About the Author
<PERSON><PERSON><PERSON><PERSON> has around 21 years of experience and
is currently working as a chief information officer. He is
responsible for creating and managing the company's IT
strategy, operations, and resources. He is responsible for
the alignment of the company’s goals and objectives with IT
strategy and operations. He ensures that the IT performance
operates at high productivity and delivers high business
value to the customers and employees. He establishes
policies and the best practices in the company, using the
right IT governance to meet compliance requirements. He is
responsible for ensuring IT infrastructure’s data quality,
integrity, and security standards.


He has a Master’s in Industrial Systems Engineering from
the Georgia Institute of Technology (1997) and a Bachelor’s
in Aerospace Engineering from the Indian Institute of
Technology, Madras (1993). He is an individual member of
Oracle JCP. He has published papers on IEEE, JIMR, Johr
Online, and the Association of Data Scientists (ADaSci)
on the latest technologies. He is a TEDx Speaker and has
presented at EuroPython, Pycon, ClueCon Weekly, World RPA
Conference, and Avios conferences. He has written Packt
Publishing - Hands-On Data Structures and Algorithms with
Go and Paytech book on The Payment Technology Handbook
for Investors, Entrepreneurs, and FinTech Visionaries He has
worked extensively in FinTech Space in various verticals
such as banking, financial services, and insurance.


Bhagvan has worked for Fortune 500 companies, including
JPMC, Fidelity, Oracle, Siebel, Citi, American Express, ING,
ENBD, Pacific Corp, and Kaiser Permanente. He has provided
technology and business process reengineering consulting
guidance and implemented various new-generation
platforms for business enablement.


Bhagvan founded Quantica Computacao, the first quantum
computing startup in India. Markets and Markets has
positioned Quantica Computacao in the ‘Emerging
Companies’ section of Quantum Computing quadrants. The
company's focus is on developing quantum cryptographic
tools that will be able to provide quantum-proof data
security, which will help banking institutions protect their
transactions.


He contributes to open-source projects, blogs, and the latest
technology stacks such as Go, Python, Django, Node.js and
Java, Mysql, Postgres, Mongo, and Cassandra. He has
reviewed the Manning book Machine Learning with
TensorFlow. He has written Data Structures and Algorithms
with Golang (Packt) and Quantum Computing Solutions
(Apress). He has presented at IEEE, Avios, Astricon, Devcon,
and PyCon on topics such as Adaptive Learning and AI
Coder.


About the Reviewer
Mahima Singla is a passionate Principal Software Design
Engineer with extensive professional experience and
passion for designing and implementing robust, scalable
software solutions in the domain of Cloud Assessment,
Cloud Governance, Cloud Cost Optimization, Application
fitment for cloud, Cloud Operating Models, cutting-edge
technologies, particularly in the realms of Cloud computing,
AWS, and Kubernetes in Go language.


She is currently working in Precisely Software and is part of
the Studio Administrator Cloud project and Customer
Onboarding project. She has played a pivotal role in
architecting and developing solutions that harness the full
potential of cloud platforms. She has proficiency in AWS,
including services like AWS services EC2, S3, and Lambda,
has allowed her to create resilient and scalable applications
that align with business objectives.


Additionally, proficiency in Kubernetes reflects the
commitment to staying at the forefront of container
orchestration. She has successfully implemented and
managed containerized workloads, ensuring efficiently
managed containerized workloads, efficient resource
utilization, and high availability for applications.


Throughout her career, she has not only focused on
technical excellence but also on driving innovation and
fostering collaborative environments. As a Principal Software
Engineer, she takes pride in leading teams to deliver high-
quality solutions that meet and exceed client expectations.


She is dedicated to staying abreast of industry trends and
leveraging the latest technologies has allowed her to
contribute meaningfully to the success of the projects she
undertakes. She is quite excited about the continuous
evolution of software engineering and is always eager to
take on new challenges that push the boundaries of what is
possible in the world of cloud-native development.


Acknowledgement
Embarking on the journey of writing Go Lang in Depth has
been wonderful, and I am deeply grateful to the BPB team,
who have played a crucial role in bringing this book to
fruition. This endeavor would not have been possible
without the unwavering support, guidance, and expertise
generously provided by my parents and kids.


To my parents and kids, thank you for your unwavering
support. In the middle of this book journey, my father
passed away. I dedicate this book to his support and
guidance during this book journey. I want to express deep
appreciation to my mom, whose encouragement and
understanding have been a constant source of strength
throughout this writing journey. Firstly, my heartfelt thanks
go to the Go Lang community, whose dedication to the
framework has shaped this book. Special gratitude is
extended to the technical reviewers whose meticulous
reviews and insightful feedback enhanced the book. Their
dedication and expertise have been invaluable in refining
the content and ensuring its accuracy.


To all those at the BPB Publications house who have
contributed in various capacities, your collective efforts
have enriched this endeavor. I appreciate the collaborative
spirit that has fueled the creation of Go Lang in Depth.


Finally, to the readers, thank you for choosing this book as
your source of knowledge. May it be a valuable companion
on your journey to learning Go Lang in Depth.


Preface
Understanding the basics of digital systems and technology
is important in today's rapidly evolving world. This book, Go
Lang in Depth, covers the essential concepts that form the
backbone of digital systems.


In the fast-evolving landscape of Go Lang development,
understanding the development of apps using Go Lang is
very important for aspiring developers. There are many
open-source, popular frameworks in Go Lang. New open-
source frameworks have embraced Go Lang as the language
for their tech stack.


This book comprises 17 chapters, each a complete module
in itself, serving as your comprehensive guide to learning Go
Lang in Depth. It covers a wide array of topics, ranging from
the fundamentals to advanced techniques. Whether you're a
seasoned developer looking to expand your skills or a
newcomer eager to dive into Go Lang development, this
book has something for everyone.


Through practical examples, comprehensive explanations,
and a structured approach, this book aims to equip readers
with a solid understanding of Go Lang programming and
software engineering. Whether you are a novice or an
experienced learner, I hope this book will serve as a
valuable resource in your journey of exploring the
foundations of digital systems and technology.


Chapter 1: Go Fundamentals - In this chapter, readers
will be presented with fundamentals in Go language. The
topics like data types, variables, constants, operators, and
Go programming will be discussed.


Chapter 2: Advanced Features of Go - In this chapter,
readers will be presented with advanced features like error
handling, interfaces, typecasting, concurrency, and Mutex.


These topics will be discussed with coding examples.


Chapter 3: Go Console Applications - In this chapter,
readers will be presented with techniques to build console-
based applications. Applications will have the capability to
read input and write output. The input and the output can
be of different formats like CSV, properties, XML, and JSON.


Console applications can talk to a database, and these apps
will be built in Go. Data structures in Go will be discussed.


Chapter 4: Building Rest API - In this chapter, readers
will learn about different algorithms which can be
implemented in Go. They will also know how to develop
REST API to access data from different data sources. The
REST API can read and write data to data sources like
relational databases, no sql databases, and message
queues. REST API clients can be web applications and
mobile applications
Chapter 5: Go Web Apps - In this chapter, readers will be
presented with examples and solutions to build web
applications using Gin and Beego. Web applications will
consume REST API and communicate to databases through
the API. These web applications will be built to be scalable
and performable.


Chapter 6: RPC Apps with gRPC - In this chapter, the
reader can build RPC apps using Go. These apps are built
using Google gRPC. Readers will know about Protocol
Buffers. gRPC clients will be developed to talk to the gRPC
server. Unit tests will be written to test the clients and
server code.


Chapter 7: Go Real Life Applications - CRM- In this
chapter, readers will learn how to build a real-life application
of customer relationship management. CRM application will
be a web application that will have the capability to
maintain customer data and run campaigns for segments or
a list of customers by geography or vertical.


Chapter 8: Go Concurrent Apps- In this chapter, readers
will learn how to build concurrent applications using Go.


Readers will know how to use goroutines, channels, and
concurrency using Go. The reader will understand the
principles of concurrency and parallelism.


Chapter 9: Go Dependency Injection and SOLID - In
this Chapter, the Reader will understand dependency
injection using the Go Language. The reader will also
understand SOLID principles, which include dependency
inversion. The reader will also learn different types of
dependency injection, such as constructor, property, and
method.


Chapter 10: Containerization and Docker - In this
chapter, the reader will understand containers and Docker.


The reader will be able to develop services that can be
containerized using Docker. Docker images can be created
and posted on the Docker Hub for different Go services.


Chapter 11: Go Microservices - In this chapter, the
reader will understand the principles of microservices
architecture vs. monolithic architecture. The reader will be
able to create an architecture using Microservices and
deploy these services on the containers.


Chapter 12: Adding Security and IAM - In this chapter,
readers will learn how to build security services like
authentication, authorization, encryption, and identity
management. Readers will learn about Cross-Site Request
Forgery (CSRF) prevention middleware. Readers can
understand the security principles to build scalable and
secure web applications.


Chapter 13: Go Design Patterns–Part 1 - In this chapter,
readers can understand different design patterns like
Creational and Structural Design patterns. Code samples will
be provided for the Gang of Four Design patterns. Readers
will understand object-oriented design principles using Go
Chapter 14: Go Design Patterns–Part 2 - In this chapter,
readers can understand different design patterns, like
Behavioral Design patterns. And Object-oriented Design
Patterns. Code samples will be provided for the Gang of Four
Design patterns and Object-oriented Design Patterns.


Readers will understand object-oriented design principles
using Go.


Chapter 15: Go Performance Tuning Patterns - In this
chapter, the reader will learn how to develop responsive and
performant Go applications. Different performance tuning
and optimization patterns are presented with code samples,
and the reader will understand these techniques while
building web applications.


Chapter 16: Web Web App Blueprints - In this chapter,
readers will be presented with web app blueprint
applications. Readers will understand the design and
architecture principles to build scalable web applications.


The blueprint solutions will have tips and techniques to build
responsive web applications that are performant.


Chapter 17: Go Mobile Applications Blueprints - In this
chapter, readers will be presented with mobile app blueprint
applications. Readers will understand the design and
architecture principles to build scalable mobile applications.


The blueprint solutions will have tips and techniques for
building performant mobile applications.


Code Bundle and Coloured
Images
Please follow the link to download the
Code Bundle and the Coloured Images of the book:
https://rebrand.ly/220b81
The code bundle for the book is also hosted on GitHub at
https://github.com/bpbpublications/Go-Lang-in-
Depth. In case there’s an update to the code, it will be
updated on the existing GitHub repository.


We have code bundles from our rich catalogue of books and
videos available at https://github.com/bpbpublications.


Check them out!
Errata
We take immense pride in our work at BPB Publications and
follow best practices to ensure the accuracy of our content
to provide with an indulging reading experience to our
subscribers. Our readers are our mirrors, and we use their
inputs to reflect and improve upon human errors, if any, that
may have occurred during the publishing processes
involved. To let us maintain the quality and help us reach
out to any readers who might be having difficulties due to
any unforeseen errors, please write to us at :
<EMAIL>
Your support, suggestions and feedbacks are highly
appreciated by the BPB Publications’ Family.


Did you know that BPB offers eBook versions of every book published, with
PDF and ePub files available? You can upgrade to the eBook version at
www.bpbonline.com and as a print book customer, you are entitled to a
discount on the eBook copy. Get in touch with us at :
<EMAIL> for more details.


At www.bpbonline.com, you can also read a collection of free technical
articles, sign up for a range of free newsletters, and receive exclusive
discounts and offers on BPB books and eBooks.


Piracy
If you come across any illegal copies of our works in any form on the internet,
we would be grateful if you would provide us with the location address or
website name. Please contact <NAME_EMAIL> with a link to
the material.


If you are interested in becoming an author
If there is a topic that you have expertise in, and you are interested in either
writing or contributing to a book, please visit www.bpbonline.com. We have
worked with thousands of developers and tech professionals, just like you, to
help them share their insights with the global tech community. You can make
a general application, apply for a specific hot topic that we are recruiting an
author for, or submit your own idea.


Reviews
Please leave a review. Once you have read and used this book, why not leave
a review on the site that you purchased it from? Potential readers can then
see and use your unbiased opinion to make purchase decisions. We at BPB
can understand what you think about our products, and our authors can see
your feedback on their book. Thank you!
For more information about BPB, please visit www.bpbonline.com.


Join our book’s Discord space
Join the book’s Discord Workspace for Latest updates,
Offers, Tech happenings around the world, New Release and
Sessions with the Authors: <AUTHORS>
Table of Contents
1. Go Fundamentals
Introduction
Structure
Objectives
Basic programming in Go
Data types
Basic types
Composite types
Reference types
Interface types
Variables
Constants
Operators
Conditional statements
if condition statement
Switch statement
Strings and arrays
Strings
Arrays
Maps
Pointers and structures
Pointers
Structures
Recover, defer, and panic
Conclusion
Points to remember
2. Advanced Features of Go
Introduction
Structure
Objectives
Error handling
Interfaces
Empty interface
Type assertion
Type switch
Zero value of interface
Type casting
Concurrency
Mutex
Conclusion
Points to remember
3. Go Console Applications
Introduction
Structure
Objectives
Console-based app in Go language
Different input and output formats
Text format
JSON
XML
Database interaction
Data structures
Conclusion
Points to remember
4. Building REST API
Introduction
Structure
Objectives
Algorithms
Bubble sort
REST API in Go
REST API interacting with relational database
REST API interacting with No SQL database
REST API interacting with message queues
Go best practices
Conclusion
Points to remember
5. Go Web Apps
Introduction
Structure
Objectives
Developing web app in Go
Getting database connection
Retrieving employee from the given database Id
Retrieving all employees from the database
Adding employee to the database
Updating the employee in the database
Deleting the employee from the database
Developing web app using Gin and REST API
Developing Web App using Beego
Conclusion
Points to remember
6. RPC Apps with gRPC
Introduction
Structure
Objectives
RPC apps in Go Lang
Protocol buffers
gRPC clients
gRPC server
Conclusion
Points to remember
7. Go Real Life Applications—CRM
Introduction
Structure
Objectives
CRM application in Go
REST API
Unit testing the REST API
Web app
Conclusion
8. Go Concurrent Apps
Introduction
Structure
Objectives
Concurrency and parallelism principles
Goroutines
Channels
Channel types
Channel patterns
Concurrent apps in Go
Conclusion
9. Go Dependency Injection and SOLID
Introduction
Structure
Objectives
SOLID principles
Single responsibility principle
Open/Closed principle
Liskov Substitution Principle
Interface Segregation principle
Dependency Inversion Principle
Dependency injection
Dependency injection types
Creating structs and interfaces
Manual dependency injection
Constructor injection
Setter/method injection
Dependency injection frameworks
Google’s Wire
Facebook’s inject
Uber’s dig
Dingo
Choosing the right framework suitable for you
Conclusion
10. Containerization and Docker
Introduction
Structure
Objectives
Containers
Docker
Docker Registry
Docker Compose
Docker networks
Go Services
Dockerized Go Services
Conclusion
11. Go Microservices
Introduction
Structure
Objectives
Monolithic architecture
Microservices architecture
Microservices in Go
Containerized Go microservices
Conclusion
12. Adding Security and IAM
Introduction
Structure
Objectives
Basic security services
Advanced security services
Security principles
Securable web applications
Public key infrastructure
SSL/TLS certificates
Code signing
Application code signing
SSH keys
Trust exchange
Real-life examples
Securing REST API
Unit testing the REST API
Create Customer
Get Customers
Update Customer
Delete Customer
Get Customers after Delete
Conclusion
13. Go Design Patterns—Part 1
Introduction
Structure
Objectives
Creational design patterns
Abstract factory pattern
Builder pattern
Factory method pattern
Prototype pattern
Singleton pattern
Structural design patterns
Adapter pattern
Bridge pattern
Composite pattern
Decorator pattern
Façade pattern
Conclusion
14. Go Design Patterns—Part 2
Introduction
Structure
Objectives
Behavioral design patterns
Chain of responsibility pattern
Command pattern
Interpreter pattern
Iterator pattern
Mediator pattern
Memento pattern
Observer pattern
State pattern
Strategy pattern
Template method pattern
Visitor pattern
Object-oriented design patterns
GRASP patterns
Conclusion
15. Go Performance Tuning Patterns
Introduction
Structure
Objectives
Go apps performance tuning
Go apps code profiling
Go web apps challenges
Recursion
Dynamic memory allocation
HTTP request and response
Other challenges
Go web apps performance tuning
Go performance patterns
Concurrency handling
CPU intensive tasks
Web traffic handling
Go scalability patterns
Conclusion
16. Go Web App Blueprints
Introduction
Structure
Objectives
Web app in Go recipes
Building scalable and responsive web app
Tools and techniques
Go Lang web app challenges
Web app design principles
Web app architecture principles
Conclusion
17. Go Mobile Applications Blueprints
Introduction
Structure
Objectives
Challenges in building mobile apps
Go Lang for mobile apps
Mobile app in Go recipes
Building scalable and responsive mobile app
Mobile app design principles
Mobile app architecture principles
Clean architecture
Hexagonal architecture
Conclusion
Index