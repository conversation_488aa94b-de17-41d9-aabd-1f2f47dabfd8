# 17 CHAPTER 17 Go Mobile Applications Blueprints

design and architecture principles to build scalable mobile applications. 
tips and techniques to build mobile applications that are performant.

    Challenges in Building Mobile Apps
    Go Lang for mobile apps
    Mobile App in Go Recipes
    Building scalable and responsive mobile app
    Mobile app design principles
    Mobile app architecture principles

Objectives
Go language-based frameworks are popular in web and
mobile applications. Mobile apps are being developed as
native, hybrid, and mobile web applications. In this chapter,
we are going to discuss Go recipes for building mobile
applications. You will be able to build scalable and responsive
mobile applications. Mobile application architecture and
mobile design principles will be presented.


Challenges in building mobile apps
The challenges are as follows:
Device fragmentation: Developers face an ongoing
battle with device fragmentation. There are countless
devices on the market, each with its own screen size,
resolution, operating system version, and hardware
capabilities. Ensuring an app runs smoothly across this
vast spectrum can be daunting.


An app like Spotify needs to function seamlessly on
high-end devices like the latest iPhone as well as on
older, less powerful Android devices. This means
extensive testing and optimization to cater to a wide
audience.


Failing to address device fragmentation can lead to
poor user experience, negative reviews, and app
abandonment.


Developers
must
adopt
responsive
design and thorough testing to mitigate this.


Security concerns: With increasing data breaches
and cyber-attacks, security is a significant challenge.


Ensuring secure data storage, robust authentication,
and regular updates is crucial to protect user
information.


Banking apps, such as those from HDFC or ICICI,
require top-notch security features to protect sensitive
financial data. Implementing end-to-end encryption and
multi-factor authentication are just some measures
taken to ensure user safety.


A single security breach can erode user trust and
damage an app’s reputation. Vigilance in security
practices
is
non-negotiable
to
maintain
user
confidence.


User experience (UX): Creating an intuitive and
engaging UX is vital. Users have high expectations and
little tolerance for apps that are hard to navigate or
slow
to
respond.


Striking
a
balance
between
functionality and simplicity is an ongoing challenge.


The success of apps like Airbnb lies in their seamless
UX design. They offer a straightforward booking
process, user-friendly interfaces, and quick load times.


This
ease
of
use
drives
user
satisfaction
and
engagement.


Poor UX can lead to high uninstall rates and low user
engagement. Continuous UX research, prototyping,
and user testing are essential to keep the app relevant
and user-friendly.


Performance optimization: Performance issues such
as slow load times, crashes, and excessive battery
consumption can be detrimental. Users expect apps to
be fast and reliable, irrespective of their device or
network conditions.


Apps
like
PUBG
Mobile
need
to
deliver
high-
performance
gaming
experiences.


This
involves
optimizing graphics, minimizing latency, and reducing
battery drain, all while ensuring smooth gameplay on
various devices. Failure to optimize performance can
lead to user frustration and high churn rates.


Continuous performance monitoring and optimization
are key to user retention.


Market competition: The app market is fiercely
competitive, with millions of apps vying for users’
attention. Standing out requires a unique value
proposition and effective marketing strategies.


TikTok’s success can be attributed to its innovative
features and aggressive marketing. Continuous feature
updates and trends keep users engaged and attract
new
ones.


Without
differentiation
and
strong
marketing, even well-developed apps can struggle.


Developers
need
to
focus
on
innovation,
user
engagement, and strategic marketing to thrive.


Building a successful mobile app is a complex journey,
filled
with
numerous
challenges.


From
device
fragmentation and security concerns to UX design and
performance optimization, each aspect requires careful
attention and continuous iteration. By addressing these
challenges head-on, developers can create compelling
and resilient apps that resonate with users and stand
out in a crowded market.


Go Lang for mobile apps
Go Lang was created by Google. It is an opensource
language and popular for concurrency and performance. Go
Lang apps can handle concurrent requests and can scale as
a web app or mobile app. These applications interact with
the backend, which can be REST API or any other API.


Let us look at an e-commerce system that has an online web
app and a mobile application. These applications help the
customer to order products from a catalog on the e-
commerce system. Vendors update their products and stock
after delivering and checking the inventory. Vendors have
the web and mobile applications to update the system. Since
the orders can come from different users and locations, the
system needs to be able to handle the load of concurrent
requests. Go language-based tech stack provides the
capability to scale the system to add more products from
different categories and geographic locations. This system
needs to integrate with order delivery and logistics systems.


The ordering system needs to be integrated with the
payment gateway, and payments need to be processed from
the customers and channeled to the vendor accounts. The
system provides a chat tool for the customers to interact
with the digital assistant/chatbot and the human personnel
for queries.


Many companies have started using Go Lang to build
enterprise mobile and web applications. Go Lang is
becoming popular in retail, telecom, insurance, and finance
domains. These apps are based on the Go Lang tech stack,
which makes them scalable and secure. These applications
have failover, reliability, efficiency, and auto-recovery
features. The system will have customer support and
assistance to answer customer queries and issues. Go Lang
can utilize multi-core, multi-task handling, and scaling to
create multiple modules. Developers can learn the language
quickly and use it to build enterprise web and mobile
applications.


Creating mobile applications using Go (Golang) involves
leveraging its robust and efficient nature to build high-
performance apps. Go is particularly well-suited for backend
development, but it can also be used for mobile applications
through various strategies. One approach is to write all-Go
native mobile applications, which allows developers to utilize
Go's concurrency model and performance benefits directly
on mobile platforms. This method ensures that the
application is both fast and efficient, taking full advantage of
Go's capabilities.


Another strategy is to create SDK applications by generating
bindings from a Go package and invoking them from Java on
Android or Objective-C on iOS. This approach allows
developers to write core logic in Go and then integrate it
with the native mobile code. This method is particularly
useful for developers who want to maintain a single
codebase for the core functionality while still leveraging the
native features and UI components of each platform.


Using tools like Go Blueprint can significantly streamline the
process of setting up Go projects. Go Blueprint provides a
standardized project structure, making it easier to manage
and scale applications. It supports various frameworks such
as Gin, Fiber, and Echo and offers features like database
integration, CI/CD workflows, and more. This tool helps
developers focus on writing application logic rather than
worrying about the initial setup and configuration.


Overall, Go's simplicity, performance, and concurrency
model make it an excellent choice for mobile application
development. Whether you are building a fully native app or
integrating Go with existing mobile frameworks, the
language offers a powerful and efficient way to develop high-
quality mobile applications.


Now, let us look at how to build a mobile app in Go Lang. We
will look at different recipes.


Mobile app in Go recipes
Go language is becoming popular for web and mobile
applications in different enterprises. Mobile applications are
user-friendly and perform well compared to web applications.


Web applications will have the front end on the server. A web
server serves the content for the web application. The
backend for mobile and web will be REST (representational
state transfer) API. REST is based on HTTP protocols. HTTP
methods that are used in REST are GET, PUT, POST, and
DELETE. Go Lang is popular because it is closer to C/C++
and can communicate well with C-shared packages. Go
mobile helps generate a package that the mobile
applications can use. This package is generated using cgo
tool. This package helps in talking to C code. First, you need
to write a main function which does not have any calls. This
go file can be converted into a shared package. This package
has a .h file and a .so file. Exported methods are in the .h
file. Gomobile has options to have the target as iOS and
Android. Go mobile has issues like limited data types and no
support for BitCode. Let us look at the commands for
building the app as iOS or Android:
gomobile bind --target=ios
gomobile bind --target=android
Gomobile generates a .aar/jar for Android and
.Framework for iOS
The backend code from the Android or iOS app built by the
mobile tool will interact with REST API. The REST API can be
used in any tech stack based on the Go language or any
other language. This backend REST API will interact with the
database and other data sources to retrieve and update the
data. This architecture style is termed as stateless
architecture, as most of the data is retrieved and updated
using the requests and responses. No data is stored in the
session. You can have master data stored in the caching
service. This helps in the mobile app response time. The
application can be scaled easily with the stateless
architecture.


To recap from Chapter 4, Building Rest API, the following
snippet is about the Gin framework:
In Go language, you can build REST API, which interacts with
relational databases to get and store data. You can use the
Gin REST framework. It is based on an HTTP Router, which
provides features like routing and form validation. You can
use GORM to store data in the relational database.


First, let us look at how to create an object model in Go Lang
based on a relational database model. In the models
package, we create an employee object, which is mapped by
GORM to a relational database table. The object fields are
mapped to relational database column types.


Regarding Beego, the following is the summary from Chapter
5, Go Web Apps:
In the Go language, you can use the Beego web framework
to develop web applications. Beego framework is an open-
source framework that has application tools, an ORM
framework, and other packages. ORM framework helps cut
down the ordeal of developing the relational schema and
mapping it to object classes and names of the attributes.


Beego is used to build REST APIs, which can be integrated
with open-source JavaScript frontend frameworks. We can
build a Beego web application using the Beego and SQLite
database. Beego framework helps develop web apps at an
affordable cost.


Kit is another framework that provides the capability to build
web applications interacting with databases and
microservices. The services can be interoperable, and non-
technical users can easily create web applications. For a
developer, it is tedious as every step is through the web
pages to create an application.


You can use Echo, which provides features for Http request
handling, building microservices based on REST protocol,
and securing the web application with SSL. We looked in
Chapter 4, Building REST API, Fiber framework based on Fast
HTTP.


You can use Fiber to design and develop REST APIs in Go
Lang. Fiber is an open-source REST API framework built on
the principles of Express.js with Fast HTTP.


Fast HTTP is better than the basic http/net package. Fast
HTTP performs better because of worker pool versus memory
allocation for different go routines in the http/net package.


It is not compatible with HTTP/2 and the Beego web app
framework. It can handle 100000 transactions per second
and 1 million network connections.


Martini can be used to build Go Lang web applications and
REST API-based services. It can also integrate with third-
party services. Martini is well documented and adopted by
many developers.


Let us look at how to build a scalable and responsive web
application.


Building scalable and responsive mobile app
Go language is popular as it can be used for mobile app tech
stack. These mobile apps are scalable and responsive.


Mobile apps can be rendered on different types of devices.


The devices can be based on the Windows/Android/IOS
operating system. Native mobile applications can be
developed using Android, Objective C, and Go Mobile. You
can have native apps that cross the platform using Flutter
and React Native. Mobile web apps can be built using React,
Angular, and Vue.js frameworks. Hybrid mobile applications
based on Android/IOS-friendly native browsers and
HTML/JS/CSS are also popular.


Mobile application requirements and architecture are
different from web applications. Mobile apps can run on
smartphones and tablets. The apps should be quick in
response and user-friendly. The development environment
(android/ios/mobile web) can debug and execute the mobile
app on the emulator/simulator/embedded browser/desktop
browser. The other important requirements are related to
security and compliance. In each domain, you have
regulations, laws, and policies to follow. For example, to
handle payments in an e-commerce app, security, and data
standards are important. Developers need to have skills and
knowledge to understand compliance requirements other
than standard mobile app tech stacks (android/ios/mobile
web). Having the prototypes done before the development
and getting user opinions about the journeys is very
important. This helps to cut down the iterations of the
development required to get the mobile app live. Let us look
at the mobile app tech stack:
Figure 17.2: Go Mobile App Architecture
Mobile app architecture consists of a mobile presentation
layer, service layer, database layer, and external API layers.


Having a good mobile app architecture defined early helps in
scaling the mobile app and having a good responsive and
performant app. Mobile architecture needs to support
communication with different services and APIs required for
the app to be responsive and performing. Mobile apps can
have features related to notifications, SMS, REST API
support, and local storage. You need to be able to add new
features/requirements in the defined architecture.


Architecture needs to be scalable to handle new groups of
users, business lines, geography, products, and functions.


The goal is to have elastic capability to handle growing app
users.


Creating scalable and responsive mobile apps using Go Lang
involves leveraging its unique attributes to maximize
performance and maintainability. At the heart of Go Lang is
its powerful concurrency model, which utilizes goroutines to
handle numerous tasks simultaneously. This is crucial for
mobile applications that need to be responsive and efficient,
particularly when dealing with real-time updates or
background processes.


Modularity is another key principle. By structuring your code
into reusable packages, you maintain a clean separation of
concerns, making the app easier to scale and maintain. This
modular approach allows for easier debugging and testing
and facilitates the integration of new features without
disrupting the existing functionality. Adopting clean
architecture principles, such as separating domain logic from
the UI and infrastructure, enhances the app's robustness and
flexibility.


Furthermore, Go Lang's cross-compilation capabilities
simplify the process of deploying applications across
different platforms. This feature ensures that developers can
write code once and run it anywhere, reducing the effort
required to support multiple operating systems. This
consistency in the build process enhances reliability and
speeds up time-to-market.


Building scalable and responsive mobile apps with Go Lang
involves leveraging its concurrency model, embracing
modularity, and utilizing cross-compilation. These principles
ensure that your mobile applications are not only efficient
and scalable but also easier to maintain and enhance. By
following these design principles, developers can create
robust and performant mobile applications that meet the
demands of modern users.


To make the architecture scalable, you need to perform the
following tasks before the start of development:
Types of devices to be supported
Current network latency problems in the geographical
areas of implementation
Current
network
connectivity
issues
in
the
geographical areas of implementation
Techniques to avoid latency issues
Techniques to avoid network connectivity
User
interface
response
times
and
navigation
capabilities on the devices
Techniques to support push notifications
Creating a development team with the required skills
Reusable components identification in the design and
architecture modules
Sustainability requirements
Future functional features in the roadmap
Mobile app security requirements like authentication
and authorization
Development plan and time frame identification
Testing plan and time frame identification
Device requirements are very important for the development
and testing of a mobile app. You need to identify the
following requirements early on:
Resolution requirements
Screen sizes to be supported
Memory requirements of the app
Memory of the devices to be supported
CPU of the devices to be supported
Storage of the devices to be supported
Now, let us look at design principles for designing web
applications.


Mobile app design principles
Designing a mobile app involves looking at various factors
like user experience and user journeys. The mobile app
needs to be designed to be user-friendly and needs to
support cross-platforms and different devices. The mobile
app must support different types of networks and associated
bandwidth and connectivity conditions. The geographies
where the mobile app will be used may have
high/medium/low bandwidth networks.


The design needs to be a factor where the app is going to be
used, like Tier1/Tier2/Tier3/… cities and towns. The
infrastructure capabilities of the geographies need to be
considered during the design. For example, a video call can
switch to an audio or chat call based on the varying
bandwidth conditions. The user can switch, or the call can be
switched to different modes automatically, and the user will
be notified. The computation and processing capabilities of
the device need to be considered if any computation and
time-consuming task needs to be performed on the device.


Most of the time, time-consuming tasks are performed on
the server-based services, and the service calls are invoked
by the mobile app.


Designing user experience involves having capabilities for
the user to navigate across the mobile app pages. Menus
need to be present for the user to navigate across the
pages/screens. The notifications and the alerts need to be
made in real-time. The design needs to handle events and
respond to user actions in real-time. For example, in a
trading app, getting the latest quote is important for the user
to put the buy/sell order. Mobile apps need to have
personalized menus and features. The color schemes, fonts,
icons, and typography need to conform with the cultural
standards and the accessibility requirements.


Mobile app needs to have an easy-to-use interface and follow
the minimal UI design methodology. This will help gain more
users as they like the app's ease and friendly user
experience. Across the application, themes need to be
maintained. Messaging and responding to the user with
notifications, alerts, and modal screens help the user to
navigate and complete the user journeys. Icons, pictures,
and help messages show the user the direction to go next to
execute the process.


There might be accessibility requirements such as support
for the following:
Visually impaired users
Hearing impaired users
Mobility impaired users
Color blind users
The design of the mobile app needs to provide images with
alternative text, navigation by keyboard, and videos with
captions. The design also needs to support labels for
buttons, breadcrumbs, progress indicators, sound-based
alerts, voice support, and help for navigation.


Mobile app design needs to support the following gestures:
Pinches
Swipes
Taps
Zoom in
Zoom out
When designing mobile applications with Go Lang,
developers are afforded a unique set of principles that
elevate the overall development process. At the core of Go
Lang's appeal is its simplicity and clarity, which translate into
fewer errors and more maintainable code. This language
encourages a clean and readable code structure that is
crucial for effective collaboration and long-term project
viability.


Concurrency is another standout feature of Go Lang, pivotal
for mobile apps that demand high responsiveness and
performance. Goroutines enable lightweight thread
management, allowing multiple processes to run
simultaneously without compromising efficiency. This makes
Go Lang particularly suited for mobile applications that must
handle numerous tasks concurrently, such as real-time
updates and background processing.


Modularity in Go Lang is essential for building scalable
mobile applications. By organizing code into reusable and
independent packages, developers can maintain a clear
separation of concerns. This not only simplifies debugging
and testing but also fosters an environment where updates
and new features can be integrated seamlessly. Coupling this
with clean architecture principles, such as segregating
domain logic from UI elements, further enhances the app’s
robustness and flexibility.


Cross-compilation is another powerful feature of Go Lang,
simplifying the process of deploying applications across
various platforms. This capability ensures that developers
can write code once and run it anywhere, minimizing the
effort required to support multiple operating systems. This
consistency in the build process enhances reliability and
accelerates time-to-market.


In conclusion, Go Lang’s simplicity, powerful concurrency
model, modularity, and cross-compilation capabilities make it
an excellent choice for mobile app development. These
principles ensure that mobile applications are not only
efficient and scalable but also easier to maintain and
enhance, paving the way for future growth and innovation.


By embracing these design principles, developers can create
robust and performant mobile applications that meet the
demands of modern users.


Now, let us look at the design principles. We will start with
user interface design principles.


The following are the UI design principles:
Create user interface prototypes to get user opinions
early
Always keep in mind the user navigation, states, auto
layout, and variables while designing
Split the complex process into multiple tasks
Each screen needs to have a specific task and goal
assigned
Do not try to abstract the key information to the user
No screen can take more than three taps/clicks on the
touchscreen (3 click rule)
Persist the screen data as the user enters the
information
The app needs to be accessible and work on different
devices
The text needs to be formal and help the user navigate
Colors, text, and tips need to be user-friendly
Readers might be interacting with users’ fast (F
pattern) eye movements for text-dense pages.


Readers might use linear and focused (Z pattern) eye
movements for rich screens.


Users need to type minimal information to perform the
task
(dropdowns/combo
boxes/clickable
options/autocomplete/prefill)
Now, let us look at the design principles that need to be
followed while designing the presentation, service, data
access, and external API integration layers.


Let us revisit the SOLID principles covered in Chapter 9, Go
Dependency Injection and SOLID. Robert C. Martin created
the SOLID principles, which are design tenets for creating
and developing software. These principles help make
software extensible, scalable, and enhanceable.


The SOLID principles are as follows:
Single Responsibility Principle (SRP)
Open/Closed Principle (OCP)
Liskov Substitution Principle (LSP)
Interface Segregation Principle (ISP)
Dependency Inversion Principle (DIP)
Now, let us look at the principles of mobile app architecture.


Mobile app architecture principles
Go Lang is becoming popular for its scalability and many
applications are nowadays being written in this language.


The term The Language of the Cloud is associated with Go.


You can use Go to build mobile, desktop, console, and web
applications. The architecture specific to Mobile applications
is based on patterns.


Crafting mobile applications with Go Lang requires a keen
understanding of its architectural principles to maximize
efficiency and maintainability. Central to this approach is
leveraging Go's simplicity and concurrency capabilities. With
goroutines and channels, Go Lang excels in handling multiple
tasks concurrently, a vital feature for creating responsive
and fast mobile applications.


Another critical aspect is the modularity of the code. By
structuring the application into modular packages,
developers can enhance both maintainability and scalability.


This modular approach not only keeps the codebase
organized but also facilitates easier updates and debugging.


Coupling this with clean architecture principles further
refines the structure. Clean architecture, with its emphasis
on separating domain logic from the UI and infrastructure,
ensures that the core functionality of the app remains
unaffected by changes in the external components.


Cross-compilation is another significant advantage of Go
Lang. This feature allows developers to build applications
across different platforms with ease, ensuring consistency
and reliability. The ability to compile code for various
operating systems and architectures without major
modifications streamlines the development process and
accelerates deployment cycles.


In summary, adopting Go Lang for mobile app development
involves a blend of simplicity, concurrency, modularity, and
cross-compilation. These principles collectively contribute to
building robust, efficient, and scalable mobile applications
that are easier to maintain and enhance over time.


For a mobile app, the following are the important decisions
related to architecture:
Can we add more requirements easily?
Can more users be added to the system?
Is the system easily testable?
Can the system handle a high concurrent number of
users?
What is the target date for the system's completion?
What is the cost of building the system?
Can we buy a mobile app that has the same features?
Will the user’s mobile device be in a Tier1/Tier2/Tier 3
network bandwidth place?
Are there known network outages in the area where
this mobile app is used?
Is there a latency expected because of connectivity and
network issues?
How frequently do we need to change the app and the
tech stack?
Can the system's architecture handle multiple OS,
namely Android/IOS/Windows/Mobile web?
Is the mobile app maintainable, and can it be
supported for issue resolution?
Do we need to gather analytics regarding who is trying
to download/install/use?
Does the system require monitoring, and does it need
to be efficient?
Can we have reusable code and components in the
design and the architecture?
Do we need a regression testing suite and an
automated testing suite for the mobile app and
backend?
Do we need to secure the mobile app?
Do we need to consider data security at rest, in transit,
and in motion?
Do
we
need
to
consider
the
device's
storage/memory/computing power?
Do
we
need
to
integrate
with
mobile
device
management systems?
Does the app need to be responsive and have good
performance on all devices?
Do we need a content delivery network to speed up the
static content delivery?
Do we need to use the app's energy consumption and
data usage?
Do we need to make the system modular and apply
design patterns and abstraction?
Do we need logging, auditing, fault management,
failover, and self-healing in the system?
Do we need two-factor/multi-factor authentication and
authorization requirements?
Do we need to have locational intelligence, device
types, and mapping API in the architecture?
User experience is another area where we need to consider
the app's users, use cases, devices used by the business
personnel/consumers, B2B/B2C/C2C apps, offline usage, and
network connectivity. Regarding the app development, you
need to look at the cost, competent people, ownership, and
infrastructure cost. User experience needs to consider
factors like look and feel, user interaction, stability,
performance, navigation, and color schemes/themes.


Recapping from Chapter 16, Go Web Application Blueprints,
different architecture methodologies for developing mobile
apps. The mobile app will have a mobile user interface, and
the backend-based on rest services.


Now, let us look at the different architecture methodologies.


First, let us look at the clean architecture methodology.


Clean architecture
Clean architecture is about structuring your mobile app in a
way that makes it maintainable, scalable, and testable. Let
us break it down.


The key concepts are as follows:
Separation of concerns: This is about dividing your
app into distinct sections, each handling a single piece
of functionality. The goal is to ensure that changes in
one part do not affect others, making maintenance
easier.


Dependency rule: High-level modules should not
depend on low-level modules. Both should depend on
abstractions. This principle ensures that core business
logic is not dependent on the details of implementation,
like database or UI.


Entities: These are your business objects that
encapsulate the core rules of your application. They
should be free of frameworks and dependencies,
making them easy to test and adapt.


Use cases: These define the application-specific
business rules. They encapsulate and implement all the
use cases of the system. They orchestrate the flow of
data to and from the entities.


Interface adapters: These convert data from the
format most convenient for entities and use cases to
the format most convenient for whatever database or
UI framework you are using. They isolate the business
logic from changes in the presentation or database
frameworks.


Frameworks and drivers: The outermost layer,
containing frameworks and tools like UI, databases,
and other external services. This layer can be easily
swapped out without affecting the core of the
application.


Let us now look at an example. Imagine you are building a
To-Do list app:
Entities: These could be classes like Task and User.


They contain core properties and methods, like
Task.complete() or User.addTask(task).


Use
cases:
These
would
be
the
actions
your
application performs, like adding a task, completing a
task, or fetching a user’s tasks. They might be methods
like
AddTaskUseCase.execute()
or
CompleteTaskUseCase.execute().


Interface adapters: If your use case interacts with a
database to fetch or store tasks, the adapter will
handle the translation of data formats.


Frameworks and drivers: Here, you have your UI
code that shows the tasks in a list, perhaps a database
like SQLite that stores tasks, and networking code if
you are syncing tasks across devices.


Let us look at the challenges in this architectural style:
Maintainability: With clean architecture, your code is
modular and organized, making it easier to locate and
fix bugs. Each layer operates independently, so making
changes to one does not ripple through the entire
application.


Testability: Clean architecture makes unit testing
straightforward. Because business rules are separated
from UI and other frameworks, you can test the core
logic without needing the UI or database.


Scalability: As your application grows, you can add
new features by adding new use cases or entities
without refactoring existing code significantly.


Flexibility: Need to swap out the database? With clean
architecture, this is less of a hassle. Since your
business logic does not directly depend on external
frameworks, you can replace or upgrade parts of your
system with minimal disruption.


Collaboration: Different teams can work on different
layers without stepping on each other’s toes. For
example, the UI team can work on the presentation
layer while the backend team works on the business
logic.


Clean architecture is a powerful approach to building mobile
applications, giving you a robust framework to manage
complexity and adapt to change. This way, your app not only
works well today but is also built to handle the demands of
the future.


Now, let us look at the hexagonal architecture.


Hexagonal architecture
Absolutely, hexagonal architecture—often referred to as
ports and adapters architecture—is a fascinating approach to
structuring a mobile application. Here is a fresh take on it:
The key concepts are as follows:
Core domain logic: At the heart of hexagonal
architecture is your application’s core domain logic.


This
is
where
your
business
rules
and
core
functionalities reside. It is completely agnostic to any
external systems, ensuring that changes to external
systems do not affect your core logic.


Ports: These are the entry points to your core domain.


They define the interfaces that other systems must use
to interact with your application. Think of ports as the
sockets where adapters plug in.


Adapters: These are the implementations of the ports.


They allow various external systems to communicate
with your core domain logic. Adapters handle things
like database access, UI interactions, and third-party
services.


Separation of concerns: Hexagonal architecture
emphasizes keeping your business logic separate from
the details of how it is delivered and stored. This makes
your application easier to test and modify over time.


Isolation of frameworks: By isolating your core logic
from
frameworks
and
libraries,
you
reduce
dependencies and increase flexibility. Your core logic
should be completely unaware of any frameworks.


Let us now look at an example. Imagine you are building an
e-commerce mobile app:
Core domain logic: Classes like Order, Customer, and
Product.


Methods
here
include
actions
like
Order.addProduct(product)
or
Customer.placeOrder(order).


Ports:
Interfaces
like
OrderService
and
CustomerRepository. These interfaces define how
external systems will interact with the core domain.


Adapters: Implementations of the interfaces. For
instance,
DatabaseOrderRepository,
which
implements CustomerRepository to interact with a
database, or RESTOrderService, which handles HTTP
requests.


The potential implications are as follows:
Testability: By decoupling your business logic from
external systems, you can easily test your core logic
without needing to set up a database or an external
service. You can mock the ports and test the core logic
in isolation.


Maintainability: Changes in one part of the system
(for example, switching from one database to another)
do not impact the core domain logic. You only need to
change the adapter.


Flexibility: If you need to add a new way to interact
with your system (for example, adding a new API
endpoint), you can do so by creating a new adapter
without changing the core domain logic.


Scalability: As your application grows, you can
manage complexity by keeping a clear separation
between different parts of the system. This makes it
easier to add new features and maintain existing ones.


Resilience: If an external system fails, the core
domain remains unaffected. Adapters can handle
retries, fallbacks, and other resilience strategies
without polluting the core logic.


In essence, hexagonal architecture empowers you to build
applications that are robust, scalable, and adaptable to
change. It offers a clear separation of concerns, making your
mobile app easier to test, maintain, and evolve. This
approach ensures that your app is well-prepared to meet
future demands while remaining resilient in the face of
changes and challenges.


There are architectural patterns to build mobile applications.


Those are listed as follows:
Model View Controller (MVC)
Model View Presenter (MVP)
Model-View-ViewModel (MVVM)
View Interactor Presenter Entity Router (VIPER)
Let us look at the MVC architecture pattern in a layered
architecture diagram:
Figure 17.3: MVC architecture pattern
MVC architecture for a mobile app will have an
Android/IOS/Mobile web view layer. The controller will be the
API gateway and orchestrator of the REST API Services. The
model layer can be an ORM framework. If you want to use
MVP, the model view remains the same as the MVC pattern.


The presenter in the mobile app case will be an
Android/iOS/web client that manages the data retrieval and
rendering. The difference here is that View focuses on
rendering, and the presenter manages the app’s state by
calling the REST API. The presenter manages the data
updates and pushes it to the View using the model layer—
REST API talking to the database. In MVVM, the view model is
the additional part with a clear separation of the UI data
model and the interface to be rendered. User controls on the
form or a table will be rendered using the UI model.


VIPER architecture will have a mobile user interface as a
view layer, and a presenter layer similar to the MVP pattern,
and an interactor will be the data handler for the view layer.


The router manages the navigation of the user and presents
a smooth user experience. The interactor will be sending
requests and receiving responses from the entity, which can
be REST API talking to the data sources.


Conclusion
In this chapter, we have covered topics related to mobile
apps in Go recipes, building scalable and responsive mobile
applications, mobile app design principles, and mobile app
architecture principles. We looked at different Go recipes
while building mobile native/hybrid/mobile web applications.


Different techniques and approaches were presented for
building scalable and responsive mobile native/hybrid/mobile
web applications. We learned the native/hybrid/mobile web
application design principles like loose coupling and solid
principles. We looked at Go native/hybrid/mobile web app
architecture principles and methodologies like clean
architecture and hexagonal architecture. We also looked at
different architectural patterns like MVC, MVP, MVVM, and
VIPER.


Join our book’s Discord space
Join the book's Discord Workspace for Latest updates, Offers,
Tech happenings around the world, New Release and
Sessions with the Authors: <AUTHORS>
Index
A
Arrays 21, 22
B
Beego 131
Beego, configuring 141
Beego REST API 169
Beego REST API, optimizing 169-174
Beego Web App, developing 131-140
Behavioral Design, pattern
Chain Responsibility 322-326
Command 326-328
Interpreter 329-331
Iterator 332-334
Mediator 334-336
Memento 336-338
Observer 338-340
State 341-344
Strategy 345, 346
Template Method 346-348
Visitor 349-351
Bubble Sort 76-78
C
CatalogServer 165
Channels 197
Channels, challenges 199
Channels, optimizing 198-201
Channels, patterns 202-204
Channels, types 202
Clean Architecture 386, 387
Clean Architecture, challenges
collaboration 407
flexibility 407
maintainability 406
scalability 407
testability 406
Clean Architecture, concepts
concerns, separation 406
entities 406
frameworks, drivers 406
interface, adapters 406
rule, dependency 406
use cases 406
Clean Architecture, points 404, 405
Concurrency 42-45
Concurrency, aspects 46
Concurrency, patterns 48
Concurrency, points 45
Concurrency, repository 379
Concurrency, tools 379
console-based app 50-56
Containerize Microservices 265
Containers 230
Containers, capabilities 231
Containers, visualizing 231, 232
Creational Design 302
Creational Design, patterns
Abstract Factory 302-304
Builder 304-306
Factory Method 306-308
Prototype 308-310
Singleton 310, 311
CRM 168
CRM, modules 168
Cross-Compilation 402
D
Database Interaction 67-69
Data Structures 69-73
demographics 204
demographics, factors 205
demographics, formats 206
demographics, types 204
Dependency Injection 222-224
Dependency Injection, dependency 224, 225
Dependency Injection, factors 228
Dependency Injection, frameworks
Dingo 227
Facebook's Inject 227
Google's Wire 227
Uber's Dig 227
Dependency Injection, method
constructor 226
manual 226
setter 226
struct/interfaces 225
Dependency Inversion Principle (DIP) 221, 222
DevSecOps 273
Digital Certificates, steps 281, 282
Digital Certificates, types 275
Docker 232
Docker, architecture 233, 234
Docker Compose 235, 236
Docker, history 233
Dockerize Microservice 244
Dockerize Microservice, integrating 244-246
Docker Network, configuring 237
Docker Registry 234, 235
E
Echo 376
EmbeddedStruct 36
Error Handling 31-33
F
Fast HTTP 377
Fiber 78
FlightCloner 308
G
Go Lang 2-4
Go Lang, applications 363
Go Lang, best practices 107, 108
Go Lang, challenges
Dynamic Memory, allocating 359, 360
HTTP Request, response 360-362
Recursion 358, 359
Go Lang Code, profiling 355-357
Go Lang, constants 11, 12
Go Lang Data, types
basic type 4, 5
composite 6, 7
interface 9, 10
reference 8, 9
Go Lang, factors 364
Go Lang, formats
JSON 62-64
Text 60-62
XML 64-66
Go Lang, history 2, 375
Go Lang Mobile, apps 395, 396
Go Lang, operations
database, connecting 113
database, deleting 117
database Id, retrieving 114
employee database, adding 116
employee database, updating 116
employees, retrieving 115
Go Lang, operators 12-16
Go Lang, performance 354, 355
Go Lang, pointers 23, 24
Go Lang, statements
if condition 16-18
switch 19
Go Lang, structures 24, 25
Go Lang, variables 10, 11
Go Performance Patterns, aspects
concurrency, handling 365
CPU Intensive, tasks 366, 367
web traffic, handling 367-369
Goroutines 194
Go Services 237
Go Services With REST API, utilizing 238-243
GRASP Patterns, principles
abstraction 352
controller 352
creator 351
high, cohesion 352
information, expertise 351
low, coupling 351
Polymorphism 352
pure, fabrication 352
gRPC 144
gRPC Clients, optimizing 156-161
gRPC, configuring 145
gRPC, features 144
gRPC Server 161-164
H
healthcare domain, features 288
Hexagonal Architecture 387, 388
Hexagonal Architecture, advantages 391
Hexagonal Architecture, implications
flexibility 408
maintainability 408
resilience 408
scalability 408
testability 408
Hexagonal Architecture, key concepts
adapters 407
concerns, separation 407
core domain, logic 407
framework, isolation 407
ports 407
I
Interface Segregation Principle (ISP) 219, 220
Interfaces Embedding 34-36
Interfaces Embedding, methods
Empty Interface 36, 37
Nil Interface 39-41
Type Assertion 37, 38
Type Switch 38, 39
IPhoto 317
K
kernel interaction, preventing 196, 197
kernel interaction, steps 195
Kit 376
Kubernetes 266
Kubernetes, preventing 266-268
L
lexer 330
Liskov Substitution Principle (LSP) 217-219
M
map 22, 23
Marshal() 64
Martini 377
MessagePublisher 240
Microservice 244, 253
Microservices Architecture 250
Microservices Architecture, resources 253
Microservices Architecture, visualizing 250-253
Microservices With REST API, integrating 253-261
mobile apps, architecture 400
mobile apps, challenges
device, fragmentation 394
market, competition 395
performance, optimizing 395
security, concerns 394
User Experience (UX) 395
mobile apps, features 400
mobile apps, gestures 402
mobile apps, optimizing 398-400
mobile apps, patterns 408
mobile apps, principles 401
Modularity 378
Monolithic Architecture 248
Monolithic Architecture, features 249
Monolithic Architecture, metrics 248
Monolithic Architecture, optimizing 249
Monolithic Architecture, services 250
Mutex 46
Mutex, methods 46
Mutex, uses 47
MVC Architecture 409
MySQL Service, steps 117-122
O
Object-Oriented Design (OOD) 351
Open/Closed Principle (OCP) 215-217
P
Parallelism/Concurrency, difference 193
Parallelism/Concurrency, principles 190-192
performance metrics, tools 363
PKI, key concern 275
Postman 174
Postman, operations
API, invoking 299, 300
create 294
deleting 298, 299
getting 294-296
updating 296, 297
Postman, preventing 175-177
Profile Management, features 283
Profile Management, setting up 283
Protobuf 147
Protobuf, optimizing 148-152
Protobuf, steps 147
Public Key Infrastructure (PKI) 275, 276
R
RBAC, features 271, 272
Recover/Defer/Panic 25-27
REST API 78-80
REST API, routes 96, 97
REST API, securing 288-292
REST API With MQ, interacting 98-106
REST API With NoSQL, interacting 88-95
REST API With RD, interacting 80-87
REST Web App, developing 123-129
REST Web App, interacting 130, 131
Role-Based Access Control (RBAC) 270, 271
S
SAML, process 284, 285
Security Assertion Markup Language (SAML) 284
Security, principles 274
Security, web applications
Application Code, signing 277, 278
code, signing 276, 277
Public Key Infrastructure (PKI) 275
SSH Key 278, 279
SSL/TLS, certificates 276
Trust, exchanging 280, 281
Single Responsibility Principle (SRP) 213-215
SOLID 212
SOLID, components 383
SOLID, layers 383, 384
SOLID, principles
Dependency Inversion Principle (DIP) 221
Interface Segregation Principle (ISP) 219
Liskov Substitution Principle (LSP) 217
Open/Closed Principle (OCP) 215
Single Responsibility Principle (SRP) 213-215
SQLCompiler 314
sqlite3 69
SSH Key 278, 279
SSH Key, threats
Application Data 279
Application Lifecycle, transitions 279
Application Linkage 279
attack, profiling 280
attack, tracking 280
behavioral, leakage 280
Identification 279
identity-theft, attack 280
inventory, attack 280
Localization Data, leakage 280
privacy, interacting 280
Strings 20, 21
Structural Design, pattern
Adapter 312, 313
Bridge 314, 315
Composite 315, 316
Decorator 317, 318
Facade 318-320
SWIFT 272
T
TCLM, features 287
TCLM, impact 287
TCLM, points
agent, setup 285
business unit, setup 285
certificate, integrating 286
certificate, issuing 286
Certificate Template, setup 285
DNS, integrating 286
License, managing 286
Lifecycle, managing 286
Profile, setup 285
sensor 286
sensor, connection 286
Trust Certificate, connector 286
Trust Certificate, enrolment 286
Trust Certificate, setup 285
TruckFactory 303
Trust Certificate Lifecycle Management (TCLM) 285
Type Casting 41, 42
U
UI Design, principles 402, 403
unified management, process 283, 284
UnMarshal() 64
W
web app 177
web app, architectures 384
web app, aspects 385
web app, implementing 178-187
web app, package 385
web app, principles 381, 382
web app, steps 177