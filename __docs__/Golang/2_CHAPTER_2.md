# 2 CHAPTER 2

Advanced Features of Go
Introduction
In this chapter, we delve into the advanced features of the
Go language. We will explore various topics, including error
handling, interfaces, typecasting, concurrency, and mutex.


Each concept will be accompanied by practical code samples
and in-depth explanations. Deep dive into types and
interfaces will be done to cover various aspects. After Go
fundamentals in Chapter 1, this chapter is a jumpstart into
advanced topics.


The following figure illustrates advanced features of Go
language:
Figure 2.1: Go Lang advanced features
Structure
The chapter covers the following topics:
Error handling
Interfaces
Type casting
Concurrency
Mutex
Objectives
At the end of this chapter, you will be able to understand
how to use advanced features of the Go language. You can
look at the examples and learn how to handle error handling
in different scenarios. You will also learn about interfaces,
type casting, concurrency, and mutex in the Go language.


The following integrated development environments
(IDEs) can be used:
VS Code
GoLand
Vim
Komodo
GoSimple
Go Native
The following figure shows the features of an IDE:
Figure 2.2: IDE features
IDE provides different features like debuggers, runtime
compilation and modification, source control management,
developer workspace management, plugins, and different
language support. The latest tools and plugins are provided
to enhance the capabilities of IDE to deploy on the cloud
environments.


Error handling
In the Go language, errors are handled differently from other
languages. Errors are like data types and are returned by a
method in the Go language. There are no try/catch/finally
blocks, as observed in Java and other languages. No stack
traces can be printed using errors in the Go language.


The following figure illustrates the error-handling process in
the Go language:
Figure 2.3: Error handling
Error interface has a method error() and it returns a string.


We will learn more about interfaces later. You can define an
error by implementing the error function, which returns an
error string.


You can define it using the errors package and the fmt
package. Let us look at the first example using the errors
package using the following code snippet:
package main
import (
"errors"
"fmt"
)
var errorFraction = errors.New("Zero in the
denominator")
func createFraction(intvar1, intvar2 int) (int,
error) {
if intvar2 == 0 {
return 0, errorFraction
}
return intvar1 / intvar2, nil
}
func main() {
intvar1, intvar2 := 14, 0
output, error := createFraction(intvar1,
intvar2)
if error != nil {
switch {
case errors.Is(error,
errorFraction):
fmt.Println("Fraction with
Zero Denominator")
default:
fmt.Printf("error in
creating a fraction: %s\n", error)
}
return
}
fmt.Printf("%d / %d = %d\n", intvar1,
intvar2, output)
}
To compile and run the preceding Go language program,
please ensure that you have the latest Go Lang executable.


You can download it at https://golang.org/dl/. Based on
the operating system that you use, you can download the
appropriate executable.


You can verify the installation by running the following
command:
go version
The output will be based on the version that you install, as
shown here:
bhagvanarch@Bhagvans-MacBook-Air code % go version
go version go1.22.1 darwin/arm64
bhagvanarch@Bhagvans-MacBook-Air code %
After verifying the installation, you can compile and run the
error_examples.go. using the following command:
go run error_examples.go
The output is shown here:
Figure 2.4: Error example execution output
Note: In most of the software projects, error handling is considered as
a significant best practice. Developers write proper code to make the
software more reliable.


Now, let us look at the second way of handling errors.


Execute the following command similar to the previous
example:
package main
import "fmt"
func createFraction(intvar1, intvar2 int) (int,
error) {
if intvar2 == 0 {
return 0, fmt.Errorf("A fraction
cannot have a denominator as zero and '%d' as
numerator", intvar2)
}
return intvar1 / intvar2, nil
}
func main() {
intvar1, intvar2 := 14, 0
output, error := createFraction(intvar1,
intvar2)
fmt.Printf("Error is %s and Output is
%d\n", error.Error(), output)
}
You can compile and run the error_fmt_example.go. The
command is shown here:
go run error_fmt_example.go
The output is shown here:
Figure 2.5: Error FMT example execution output
You can also define the chain of function calls in the error
message that can be printed as output. This is similar to
stack traces in languages like Java and Python.


Interfaces
We have learned about interfaces in Chapter 1, Go
Fundamentals, let us do a quick recap through the following
figure:
Figure 2.6: Examples of interfaces
In the Go language, the interface is an abstract type
that consists of method signatures.


You cannot have instances of the interface.


You can create an interface by specifying the type
(keyword) and interface name interface (keyword).


More than one interface can be implemented by a data
type.


Interface embedding is supported by the Go language. The
composition of user-defined types is interface embedding.


This is different from go:embed directive. Go:embed is used to
embed in application binary the folders and files.


Now, let us look at an example of interface embeddings:
package main
import “fmt"
type embeddedStruct struct {
varSName string
}
func (eStruct embeddedStruct) display() string {
return fmt.Sprintf(“embedded Struct=%s",
eStruct.varSName)
}
type Composite struct {
embeddedStruct
varName string
}
func main() {
composite := Composite{
embeddedStruct: embeddedStruct{
varSName: “embedded User
Defned Type",
},
varName: “Composite With Embedded
Struct",
}
fmt.Printf(“composite={struct Name: %s,
composite Name: %s}\n", composite.varSName,
composite.varName)          // Marker 1
fmt.Println(“Struct name:",
composite.embeddedStruct.varSName)
fmt.Println(“display:", composite.display())
//Market 2
type displayer interface {
display() string
}
var vDisplayer displayer = composite
fmt.Println(“displayer:",
vDisplayer.display())
}
Marker1 -> Composite will have the access to
embeddedStruct fields also
Marker2 -> Composite will have access to embeddedStruct’s
methods also
You can now compile and run the instance_embeddings.go.


The command is shown here:
go run instance_embeddings.go
The output is shown here:
Figure 2.7: Interface embeddings execution output
The following figure illustrates interface embedding:
Figure 2.8: Interface embedding
The output shows how the interface embeddings are used for
composite access and the behavior methods. Interface
embedding of the displayer helps in making composite as a
displayer. An interface in Go can be thought of as a
combination of a type and its value. The type represents the
actual interface, and the value holds the data associated
with it. For instance, a Displayer interface has a method
called display.


When we use interface embedding, a struct called
EmbeddedStruct can be considered a Displayer as well.


EmbeddedStruct has a field named varSName and
implements the display method from the Displayer interface.


Empty interface
An empty interface is an interface with no methods. All types
can implement an empty interface.


Let us look at an example of an Empty interface using the
following code:
package main
import (
"fmt"
)
func identifyInterfaceType(interf interface{}) {
fmt.Printf("Type of this interface is = %T,
value of the interface is = %v\n", interf, interf)
}
func main() {
varStr := "John Smith has come"
identifyInterfaceType(varStr)
varInt := 94
identifyInterfaceType(varInt)
varCustomer := struct {
name string
}{
name: "Thomas Smith",
}
identifyInterfaceType(varCustomer)
}
You can now compile and run the
empty_interface_example.go. The command is shown as
follows:
go run empty_interface_example.go
The output is as follows:
Figure 2.9: Empty interface example execution output
Type assertion
Type assertion helps in deriving the interface type value.


interface.(X) gives the value of the concrete Type X for an
interface.


Let us look at the example of type assertion using the
following code:
package main
import (
"fmt"
)
func assert(interf interface{}) {
varS := interf.(string)
fmt.Println(varS)
}
func main() {
var varInterface interface{} = "checking
interface"
assert(varInterface)
}
You can now compile and run the
interface_assert_example.go. The command is shown as
below:
go run interface_assert_example.go
The output will be as follows:
Figure 2.10: Interface assert example execution output
If you try sending any other type, a panic message is
displayed. To rectify this error message, you can rewrite the
method assert, as given in the following code snippet:
func assert(interf interface{}) {
varS, flag := interf.(string)
fmt.Println(varS,flag)
}
If you send int instead of a string, flag will be false and
the value will be 0.


Type switch
In Go, you can use a Type switch to check the type of an
interface using a list of case statements. Each case
statement checks against a specific type, similar to a regular
switch case with specified types.


To perform a Type switch, you use the i.(type) syntax in
the switch statement, where i is the interface variable you
want to check. This allows you to handle different types
differently based on the actual type stored in the interface.


Let us look at the example of a Type switch using the
following code:
package main
import (
"fmt"
)
func identifyType(varInter interface{}) {
switch varInter.(type) {
case string:
fmt.Printf("The type is a string
and the value is %s\n", varInter.(string))
case int:
fmt.Printf("The type is int and the
value is %d\n", varInter.(int))
default:
fmt.Printf("Ths is not known
type\n")
}
}
func main() {
identifyType("This is a type check")
identifyType(93)
identifyType(231.44)
}
You can now compile and run the type_switch_example.go.


The command is shown as below:
zo run type_switch_example.go
The output will be as follows:
Figure 2.11: Switch example execution output
Zero value of interface
Nil is the zero value of an interface. Value and concrete type
of a Nil interface is Nil.


Let us look at the example of a Nil interface using the
following code:
package main
import "fmt"
type Displayer interface {
display()
}
func main() {
var varDisplay Displayer
if varDisplay == nil {
fmt.Printf("varDispaly is nil. It
is of type %T  and value is %v\n", varDisplay,
varDisplay)
}
}
You can now compile and run the
zero_value_interface_example.go. The command is
shown as follows:
go run zero_value_interface_example.go
The output will be as follows:
Figure 2.12: Zero value interface example execution output
If you invoke a method on the nil interface, panic message
Invalid memory address or Nil pointer dereference is
displayed:
Let us look at the example of a nil interface where a method
is invoked using the following code:
package main
type Displayer interface {
display()
}
func main() {
var varDisplay Displayer
varDisplay.display()
}
You can now compile and run the
nil_method_interface_example.go. The command is
shown as follows:
go run nil_method_interface_example.go
The output will be as follows:
Figure 2.13: Nil method interface example execution output
Type casting
In the Go language, types are not converted implicitly.


Variable data types can be the same, but explicit casting or
conversion is necessary in Go Lang. The term type
conversion is used in Go Lang for type casting. To convert a
value varData to a type X, you need to use X(varData).


Now, let us look at an example of type conversion using the
following code:
package main
import "fmt"
func getAverage(varArray [3]int) float32 {
total := 0
for i := 0; i < len(varArray); i++ {
total = total + varArray[i]
}
return float32(total) /
float32(len(varArray))
}
func main() {
var varArray [3]int
varArray = [3]int{2, 3, 4}
var average float32
average = getAverage(varArray)
fmt.Printf("Average = %f\n", average)
}
You can now compile and run the typecasting_example.go.


The command is as follows:
go run typecasting_example.go
The output will be as follows:
Figure 2.14: TypeCasting example execution output
The output shows how the types of variables can be
converted using type conversion in Go Lang.


Concurrency
In the Go language, concurrency is achieved through
goroutines and channels, enabling the division of complex
problems into smaller tasks that can be run concurrently. The
code structure remains consistent for each smaller chunk,
simplifying parallel execution. Go routines serve as
lightweight threads, while channels facilitate safe
communication and data sharing between concurrent
processes, ensuring efficiency and scalability in Go
programs.


The following figure illustrates parallelism:
Figure 2.15: Parallel tasks
Let us look at goroutines first and see how they are used for
concurrency. In Go Lang, the go routine gets executed with a
thread owned by it. Go routines are functions.


Concurrency and parallelism are two distinct concepts in
computing. Concurrency involves executing multiple tasks in
overlapping time periods, where tasks can start and end
independently. While they may appear to run
simultaneously, they are scheduled to progress efficiently.


On the other hand, parallelism entails executing tasks truly
simultaneously using multiple processors or cores. Single-
threaded tasks, in contrast, execute sequentially, one after
another, similar to code statements.


Concurrency Background: Hoare came up with CSP in 1978, and
Dijkstra with guarded commands in 1975. There was a need for
modeling the real world which has independently running tasks and
objects behaving. Single threaded tasks were not helpful to model the
complex world behavior.


The following figure illustrates concurrency:
Figure 2.16: Concurrent Tasks
Now, let us look at an example of goroutines using the
following code:
package main
import (
"fmt"
"time"
)
func execute(message string) {
for i := 1; i <= 5; i++ {
time.Sleep(50 * time.Millisecond)
fmt.Println(message, ": ", i)
}
}
func main() {
fmt.Println("Go concurrency example")
go execute("Go routine 1")
go execute("Go routine 2")
go execute("Go routine 3")
time.Sleep(time.Second)
fmt.Println("Example completed")
}
You can now compile and run the concurrency_example.go.


The command is shown here:
go run concurrency_example.go
The output will be as follows:
Figure 2.17: Error example execution output
The output shows how the go routines are concurrently
executed.


Now let us look at channels, and consider the following
points:
Channels provide the capability or a feature for
goroutines to share information with another go
routine.


The channel can send the data to another channel.


Channels are bi-directional.


The channel can be created by using the chan
keyword.


Now let us look at an example for channels using the
following code:
package main
import "fmt"
func shareToChannel(varChannel chan int) {
for i := 0; i < 7; i++ {
varChannel <- i
}
close(varChannel)
}
func main() {
varintChannel := make(chan int)
go shareToChannel(varintChannel)
for i := 0; i < 7; i++ {
fmt.Println(<-varintChannel)
}
}
You can now compile and run the
concurrent_channels_example.go. The command is shown
as follows:
go run concurrent_channels_example.go
The output will be as shown here:
Figure 2.18: Error example execution output
The output shows how the channels are concurrently
executed.


What are the other languages supporting concurrency: There
are many other languages supporting concurrency. Some of
them are mentioned below:
Erlang
Occam
Alef
Limbo
Concurrent ML
Java
Mutex
In the Go language, executing a Go routine is achieved using
a mutex. Go routines will have an important code part to be
executed. Mutex is used to avoid race conditions. In Go Lang,
Mutex is in the Sync package. Mutex has two methods as
follows:
Lock
UnLock
The following figure shows the two methods of mutex:
Figure 2.19: Methods used in mutex
What is a mutex?: Mutex helps in sharing an object or resource to
multiple threads. Different threads can share a file. Andrea Steinbach
and Angelika Schofer came up with mutex in their master’s research
thesis. They were studying at Rheinishce Friedrich-Wilhelms
University.


Now let us look at an example of using mutex, as shown in
the following code:
package main
import (
"fmt"
"sync"
)
var GEncounter = 0
func executor(waitGroup *sync.WaitGroup) {
GEncounter = GEncounter + 1
waitGroup.Done()
}
func main() {
var varWaitGroup sync.WaitGroup
for i := 0; i < 1000; i++ {
varWaitGroup.Add(1)
go executor(&varWaitGroup)
}
varWaitGroup.Wait()
fmt.Println("Value of encounter",
GEncounter)
}
You can now compile and run the mutex_example.go. The
command is shown as follows:
go run mutex_example.go
The output will be as follows:
Figure 2.20: Mutex example execution output
The output shows how mutex is used in Go Lang.


What is next in concurrency programming: We will discuss
concurrency patterns and building concurrent applications.


Concurrent patterns are as follows:
Generator
Channels for handling service
Multiplexing
Fan-in
Restoring sequencing
Select
Fan-in using select
Timeout using select
Quit channel
Receive on quit channel
Daisy chain
Conclusion
In this chapter, we have covered advanced topics like error
handling, interfaces, typecasting, concurrency, and mutex.


Examples were presented to demonstrate the use of the
constructs in the Go language. Interfaces were discussed in
detail, and examples were shown for the empty interface,
zero value of interface, type assertion, and type switch.


In the next chapter, we will cover topics such as basic
programming concepts in Go, data types, variables,
constants and operators, conditional statements, recover,
defer and panic, and strings. We will also look at data
structures like arrays, maps, pointers, and structures.


Points to remember
Error handling in Go Lang is done by using errors and
fmt package.


Interface embeddings help in modeling composite
access and in the creation of the behavior methods.


Type casting is necessary in the Go language. It needs
to be done explicitly.


Concurrency in Go Lang can be handled using channels
and Go routines.

