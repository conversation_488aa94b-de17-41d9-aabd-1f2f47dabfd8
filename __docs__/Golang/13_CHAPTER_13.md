# 13 CHAPTER 13

Go Design Patterns—Part1
Introduction
In this chapter, we look at design patterns like creational,
structural, and behavioral design patterns based on Gang of
Four (GOF) design patterns. Code samples are presented for
the GOF design patterns. The readers will understand object-
oriented design principles using the Go language by learning
GRASP principles. Let us look at the Go Design Patterns
related to creation and structure of objects:
Figure 13.1: Design Patterns in Go Lang
Structure
The chapter covers the following topics:
Creational design patterns
Structural design patterns
Objectives
In this chapter, we are going to look at design patterns in Go
Lang. Design patterns help resolve issues with solutions that
make the design easy and the applications can scale. The
term GOF patterns is related to a book written by four
different authors. This book was written in 1994 and was
titled Design Patterns: Elements of Reusable Object-Oriented
Software. Go Lang solutions can be built using these design
patterns.


Creational design patterns
You can use creational design patterns to design class
creators and object instantiations. These patterns help in
instantiating objects agnostic of the class to which the object
belongs. The creational design patterns are as follows:
Abstract factory
Builder
Factory method
Prototype
Singleton
Let us first look at abstract factory patterns.


Abstract factory pattern
You can use the abstract factory pattern to create a group of
objects with commonalities. An interface is defined for
instantiating the group of objects agnostic of the class to
which the object belongs. Let us look at an example of
creating truck objects. Truck has an interface with
NumOfTyres and GetModelName methods. Let us look at the
truck interface:
package main
import (
"fmt"
)
const (
CivilType = 1
)
const (
RuggedModel   = 1
StandardModel = 2
)
type Truck interface {
NumOfTyres() int
GetModelName() string
}
TruckFactory is an interface to create truck objects. You can
have CivilTruckFactory, which implements the Truck
Factory interface:
type TruckFactory interface {
Create(t int) (Truck, error)
}
type CivilTruckFactory struct{}
type RuggedModelType struct{}
func (f *RuggedModelType) NumOfTyres() int {
return 6
}
func (f *RuggedModelType) GetModelName() string {
return "Rugged"
}
type StandardModelType struct {
}
func (f *StandardModelType) NumOfTyres() int {
return 6
}
func (f *StandardModelType) GetModelName() string {
return "Standard"
}
func (i *CivilTruckFactory) Create(t int) (Truck,
error) {
switch t {
case RuggedModel:
return new(RuggedModelType), nil
case StandardModel:
return new(StandardModelType), nil
}
return nil, fmt.Errorf("There are no trucks
of type %d\n", t)
}
func CreateAbstractFactory(c int) (TruckFactory,
error) {
switch c {
case CivilType:
return new(CivilTruckFactory), nil
default:
return nil, fmt.Errorf("There is no
factory with id %d\n", c)
}
}
func main() {
civilT, _ :=
CreateAbstractFactory(CivilType)
truckM, _ := civilT.Create(StandardModel)
truck, ok := truckM.(Truck)
if !ok {
fmt.Errorf("Invalid model")
}
fmt.Printf("%v Truck has %d tyres\n",
truck.GetModelName(), truck.NumOfTyres())
}
CreateAbstractFacotry method takes the type of the truck
and returns the Truck Factory class.


You can now compile and run the abstract_factory.go.


The command is as follows:
go run abstract_factory.go
The output will be as follows:
Figure 13.2: Abstract Factory example code output
Now, let us look at the builder pattern.


Builder pattern
You can use the builder pattern to create a composite object
that has multiple objects. Let us look at an example: a truck
with multiple tires and an engine with a capacity for driving.


Let us look at the Truck struct code:
package main
import (
"fmt"
"strconv"
)
type Truck struct {
Tyres    int
Capacity int
}
type TruckBuildProcess interface {
SetTyreNumber() TruckBuildProcess
SetCapacity() TruckBuildProcess
GetVehicle() Truck
}
type TruckShopFloor struct {
tbuilder TruckBuildProcess
}
func (f *TruckShopFloor) SetBuilder(tb
TruckBuildProcess) {
f.tbuilder = tb
}
func (f *TruckShopFloor) Construct() {
f.tbuilder.SetCapacity().SetTyreNumber()
}
TruckBuilder is used to set the tyres and the
engine capacity.


type TruckBuilder struct {
t Truck
}
func (c *TruckBuilder) SetTyreNumber()
TruckBuildProcess {
c.t.Tyres = 6
return c
}
func (c *TruckBuilder) SetCapacity()
TruckBuildProcess {
c.t.Capacity = 4
return c
}
func (c *TruckBuilder) GetVehicle() Truck {
return c.t
}
func (c *TruckBuilder) Build() Truck {
return c.t
}
func main() {
shopFloor := TruckShopFloor{}
truckBuilder := &TruckBuilder{}
shopFloor.SetBuilder(truckBuilder)
shopFloor.Construct()
truck := truckBuilder.Build()
if truck.Tyres != 6 {
fmt.Errorf("It is wrong that : " +
strconv.Itoa(truck.Tyres) + " tyres found")
} else {
fmt.Printf("Truck has " +
strconv.Itoa(truck.Tyres) + "  tyres\n")
}
}
TruckBuilder is used on the shop floor to create a truck
with multiple tires and an engine with the capacity to drive.


You can now compile and run the builder_example.go. The
command is as follows:
go run builder_example.go
The output will be as follows:
Figure 13.3: Builder example code output
Now, let us look at the factory method pattern.


Factory method pattern
You can use the factory method pattern to create objects
that are agnostic to the class that they belong to. An
interface is created for object creation. Let us look at an
example where we can create trucks of different types, such
as rugged and standard models. Let us look at the factory
method pattern example code:
package main
import (
"fmt"
)
const (
RuggedModel   = 1
StandardModel = 2
)
type Truck interface {
NumOfTyres() int
GetModelName() string
}
Truck interface has NumOfTyres and GetModelName
methods.


func GetTruck(truck int) (Truck, error) {
switch truck {
case RuggedModel:
return new(RuggedModelType), nil
case StandardModel:
return new(StandardModelType), nil
default:
return nil, fmt.Errorf("Not a Known
Truck Model")
}
return nil, fmt.Errorf("Not implemented
yet")
}
type RuggedModelType struct{}
func (f *RuggedModelType) NumOfTyres() int {
return 6
}
func (f *RuggedModelType) GetModelName() string {
return "Rugged"
}
type StandardModelType struct {
}
func (f *StandardModelType) NumOfTyres() int {
return 6
}
func (f *StandardModelType) GetModelName() string {
return "Standard"
}
func main() {
truck, err := GetTruck(RuggedModel)
if err != nil {
fmt.Errorf("This model does not
exist")
}
fmt.Printf("%v Truck has %d tyres\n",
truck.GetModelName(), truck.NumOfTyres())
}
GetTruck method takes the model type and creates the
truck of different types.


You can now compile and run the factory_example.go. The
command is as follows:
go run factory_example.go
The output will be as follows:
Figure 13.4: Factory example code Output
Now, let us look at the prototype pattern.


Prototype pattern
You can use the prototype pattern to clone objects. This
pattern is useful for creating multiple cloned objects, like a
pool of objects. Let us look at an example of cloning flight
objects.


FlightCloner is the interface that has the GetClone
method, which takes the number of objects to be cloned. Let
us look at FlightCloner interface code:
package main
import (
"fmt"
)
type FlightCloner interface {
GetClone(s int) (ItemInfoGetter, error)
}
const (
Boeing     = 1
Bombardier = 2
Embraer    = 3
)
func GetFlightsCloner() FlightCloner {
return new(FlightsCache)
}
The GetFlightsCloner method returns the FlightCloner.


FlightsCache struct has a method GetClone of flight type.


Flight can be of multiple types like Boeing, Bombardier, and
Embraer. Let us look at the FlightsCache Struct code:
type FlightsCache struct{}
func (s *FlightsCache) GetClone(flight int)
(ItemInfoGetter, error) {
switch flight {
case Boeing:
newItem := *flightPrototype
return &newItem, nil
}
return nil, fmt.Errorf("Not implemented
yet")
}
type ItemInfoGetter interface {
GetInfo() string
}
type FlightEngine byte
type Flight struct {
Price  float32
Model  string
Engine FlightEngine
}
func (s *Flight) GetInfo() string {
return "" + s.Model
}
var flightPrototype *Flight = &Flight{
Price:  15.00,
Model:  "777",
Engine: Boeing,
}
func (i *Flight) GetPrice() float32 {
return i.Price
}
func main() {
flightCache := GetFlightsCloner()
if flightCache == nil {
fmt.Errorf("The current cache is
not valid")
}
firstFlight, err :=
flightCache.GetClone(Boeing)
fmt.Printf("Flight cloned is of Model type
: %s\n", firstFlight.GetInfo())
if err != nil {
fmt.Println(err)
}
if firstFlight == flightPrototype {
fmt.Errorf("firstitem cannot be
equal to the white prototype")
}
}
You can now compile and run the prototype_example.go.


The command is as follows:
go run prototype_example.go
The output will be as follows:
Figure 13.5: Prototype example code output
Now, let us look at the singleton pattern.


Singleton pattern
You can use the singleton pattern to have a single instance
of an object. This single instance is accessible at the global
level. It can also be used in a pool with a specified number of
objects. Let us look at the singleton pattern in the code:
package main
import (
"fmt"
"sync"
)
var singlock = &sync.Mutex{}
type singleton struct {
}
var singletonInstance *singleton
func getSingleton() *singleton {
if singletonInstance == nil {
singlock.Lock()
defer singlock.Unlock()
if singletonInstance == nil {
fmt.Println("Creating
singleton first time")
singletonInstance =
&singleton{}
} else {
fmt.Println("Singleton
instance is already created.")
}
} else {
fmt.Println("Single instance
already created.")
}
return singletonInstance
}
func main() {
for i := 0; i < 30; i++ {
go getSingleton()
}
fmt.Scanln()
}
getSingleton method returns the singleton instance. The
multiple calls will return the single instance after first time
creation.


You can now compile and run the singleton_example.go.


The command is as follows:
go run singleton_example.go
The output will be as follows:
Figure 13.6: Prototype example code output
Now, let us look at the structural design patterns.


Structural design patterns
Developers use structural design patterns to create objects
that belong to large, structured classes. The structural
design patterns are as follows:
Adapter
Bridge
Composite
Decorator
Façade
Let us first look at the adapter design pattern.


Adapter pattern
You can use the adapter pattern when you have different
interfaces, and there is a plan to integrate them to create an
object. The other scenario is when you want to change the
current interface to a prescribed specification.


Let us look at the example of multiple databases and a
single client interface to interact with and query them. The
database interface has the GetDatabaseInfo method, which
gives the name of the current database connected. Let us
look at the MySQL struct code:
package main
import "fmt"
type MySQL struct{}
func (mysql *MySQL) GetDBInfo() {
fmt.Println("MySQL DB")
}
type MySQLAdapter struct {
mysqlServer *MySQL
}
func (mysql *MySQLAdapter) GetDatabaseInfo() {
fmt.Println("MySQL adapter gets DB Info")
mysql.mysqlServer.GetDBInfo()
}
The MySQL database above has implemented the database
interface. Now, let us look at the PostGres database, which
implements the database interface:
type PostGres struct {
}
func (pg *PostGres) GetDatabaseInfo() {
fmt.Println("PostGres")
}
DBClient implements the Database Interface.


type DBClient struct {
}
func (client *DBClient) GetDatabaseInfo(db
Database) {
fmt.Println("Client  gets Database Info
from the specific DB")
db.GetDatabaseInfo()
}
type Database interface {
GetDatabaseInfo()
}
func main() {
client := &DBClient{}
pg := &PostGres{}
client.GetDatabaseInfo(pg)
mysqlServer := &MySQL{}
mysqlAdapter := &MySQLAdapter{
mysqlServer: mysqlServer,
}
client.GetDatabaseInfo(mysqlAdapter)
}
DBClient is instantiated, and multiple databases are created
for the client to query.


You can now compile and run the adapter_example.go. The
command is as follows:
go run adapter_example.go
The output will be as follows:
Figure 13.7: Adapter example code output
Now, let us look at the bridge design pattern.


Bridge pattern
You can use a bridge design pattern to segregate the
interface of an object from the actual implementation. The
goal is to have a loose coupling between the implementation
and the abstraction. Let us look at an example where a SQL
compiler is implemented for Postgres. SQLCompiler is an
interface that executes SQL. Each database might have
different constructs and support for special query syntax.


SQLCompiler is implemented by the PGSQLCompiler. Let us
look at the SQLCompiler interface and PGSQLCompiler struct
implementation in code:
package main
import "fmt"
type SQLCompiler interface {
ExecuteSQL(string)
}
type PGSQLCompiler struct {
}
func (pgc *PGSQLCompiler) ExecuteSQL(sql string) {
fmt.Println("PostGres executing sql- ",
sql)
}
type PostGres struct {
sqlCompiler SQLCompiler
}
func (pg *PostGres) GetDatabaseInfo() {
fmt.Println("PostGres")
}
func (pg *PostGres) SetSQLCompiler(sql SQLCompiler)
{
fmt.Println("PostGres setting SQLCompiler")
pg.sqlCompiler = sql
}
func (pg *PostGres) ExecuteSQL(sql string) {
fmt.Println("PostGres executing sql")
pg.sqlCompiler.ExecuteSQL(sql)
}
type Database interface {
GetDatabaseInfo()
SetSQLCompiler(SQLCompiler)
ExecuteSQL(string)
}
func main() {
pg := &PostGres{}
pgsql := &PGSQLCompiler{}
pg.SetSQLCompiler(pgsql)
sql := "Select * FROM Customer"
pg.ExecuteSQL(sql)
}
PostGres database is instantiated, and PGSQLCompiler is
created to set on the PostGres database. You can now
compile and run the bridge_example.go. The command is
as follows:
go run bridge_example.go
The output will be as follows:
Figure 13.8: Bridge example code output
Now, let us look at the composite design pattern.


Composite pattern
Developers use composite patterns to create a class that has
a complicated structure. A car has multiple wheels, an
engine, and other parts. We look at an example where the
composite class is created with different classes for Wheel
and Engine. Let us look at the Car struct code:
package main
import "fmt"
type Car struct {
wheels []Wheel
name   string
engine string
}
func (car *Car) AddWheel(wheel Wheel) {
car.wheels = append(car.wheels, wheel)
fmt.Print("adding :")
wheel.GetType()
}
func (car *Car) GetModel() {
fmt.Println("The Car model is", car.name)
}
type Wheel interface {
GetType()
}
type CarWheel struct {
wheelType string
}
func (wheel *CarWheel) GetType() {
fmt.Println("The wheel type is",
wheel.wheelType)
}
func main() {
car := new(Car)
car.name = "Toyota Tercel"
wheel1 := &CarWheel{wheelType: "Good year"}
car.AddWheel(wheel1)
wheel2 := &CarWheel{wheelType: "Good year"}
car.AddWheel(wheel2)
wheel3 := &CarWheel{wheelType: "Good year"}
car.AddWheel(wheel3)
wheel4 := &CarWheel{wheelType: "Good year"}
car.AddWheel(wheel4)
car.GetModel()
fmt.Println("The number of wheels in the
car are :", len(car.wheels))
}
First, wheel objects are created, and then the Car is created
with a model name and engine name. Wheels are added to
the Car to create a Car object.


You can now compile and run the composite_example.go.


The command is as follows:
go run composite_example.go
The output will be as follows:
Figure 13.9: Composite example code output
Now, let us see the decorator pattern.


Decorator pattern
The decorator pattern is used to add functionality to an
object belonging to a class that does not have that
functionality. The class interface will not be modified because
of the decorator pattern. Let us look at an example where
IPhoto is an interface that uses the method of getting a title.


Painting implements the interface IPhoto. We want to add
a YellowFrame to the Painting. YellowFrame struct is
created with the photo object, which implements the IPhoto
interface:
package main
import "fmt"
type IPhoto interface {
getTitle() string
}
type Painting struct {
title string
}
func (paint *Painting) getTitle() string {
return paint.title
}
type YellowFrame struct {
photo IPhoto
}
func (yframe *YellowFrame) getTitle() string {
return yframe.photo.getTitle()
}
func main() {
painting := new(Painting)
painting.title = "Mona Lisa"
yframe := &YellowFrame{photo: painting}
fmt.Println("The tile of the yellowFramed
painting is ", yframe.getTitle())
}
Painting is created, and the title is set on the painting.


YellowFrame object is created with a painting object that
implements the IPhoto interface.


You can now compile and run the decorator_example.go.


The command is as follows:
go run decorator_example.go
The output will be as follows:
Figure 13.10: Decorator example code output
Now, let us look at the façade design pattern.


Façade pattern
Developers can use the Façade design pattern to design an
interface that consists of multiple interface methods. Let us
now look at the Employee Façade, which has the instances of
Employee, Address, and Profile. These objects are created
using the Façade. They are persisted, modified, and deleted
through this Employee Business Façade. Let us look at the
EmployeeFacade struct code:
type EmployeeFacade struct {
employee *Employee
address  *Address
profile  *Profile
}
func newEmployeeFacade(empName string, line1
string, line2 string, ssn string) *EmployeeFacade {
fmt.Println("Creating the employee Facade
components")
employeeFacade := &EmployeeFacade{
employee: newEmployee(empName),
address:  newAddress(line1, line2),
profile:  newProfile(ssn),
}
fmt.Println("Employee Facade created")
return employeeFacade
}
func (empFacade *EmployeeFacade) saveEmployeeBO() {
empFacade.employee.saveEmployee()
empFacade.address.saveAddress()
empFacade.profile.saveProfile()
}
Now, let us look at the Employee struct code.


Employee Struct
type Employee struct {
name string
}
func newEmployee(empName string) *Employee {
return &Employee{
name: empName,
}
}
func (emp *Employee) saveEmployee() {
fmt.Println("Persisting the employee in the
datastore")
}
Let us look at the Address struct code:
Address Struct
type Address struct {
line1 string
line2 string
}
func newAddress(line1 string, line2 string)
*Address {
return &Address{
line1: line1,
line2: line2,
}
}
func (address *Address) saveAddress() {
fmt.Println("Persisting the address in the
datastore")
}
Let us look at the Profile struct code:
Profile Struct
type Profile struct {
ssn string
}
func newProfile(ssn string) *Profile {
return &Profile{ssn: ssn}
}
func (profile *Profile) saveProfile() {
fmt.Println("Persisting theprofile in the
datastore")
}
func main() {
empFacade := newEmployeeFacade("Jay Smith",
"200 South Blvd", "Boston, MA 01234", "23211111")
empFacade.saveEmployeeBO()
}
Employee, Address, and Profile objects are created
through the EmployeeFacade and persisted in the datastore
through the EmployeeFacade.


You can now compile and run the facade_example.go. The
command is as follows:
go run facade_example.go
The output will be as follows:
Figure 13.11: Facade example code output
Conclusion
In this chapter, we have covered topics related to the Gang
of Four Design Patterns and Object-oriented Design Patterns.


We have also looked at Creational and Structural Design
Patterns with examples and where they can be applied.


In the next chapter, the behavioral design patterns will be
discussed in detail with context and examples. Object-
oriented design principles and GRASP patterns were also
presented in detail.

