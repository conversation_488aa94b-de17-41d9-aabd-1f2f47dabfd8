# 5 CHAPTER 5 Go Web Apps

build web applications using Gin and Beego. 
Web applications will consume REST API and communicate to databases through the API. 
These web applications will be built to be scalable and performable.

Let us see the technologies that help build a Go web application:

The chapter covers the following topics:

    Web app in Go
    Web app using Gin
    Web app using Beego
    Web app and REST API in Go


# Developing web app in Go
In Go language, you can have web applications built using html, net/http package, and web forms. 
We are building here an employee management system. 
The application will have the capability to create, update, select, and delete customers. 

In this example, we will use MySQL and Go MySQL driver package—go-sql-driver/mysql.

Web-based applications can have forms using webforms. `.tmpl` files are used as the view, and routes are managed in Golang files. 
`.tmpl` files are loaded using the `text/template` package. 

Let us look at the examples for a web application in Go: employee_mgmt.go

```golang

package main

import (
    "fmt"
    "log"
    "net/http"
    "text/template"
)

var template_html = template.Must(template.ParseGlob("templates/*"))
func Home(writer http.ResponseWriter, request *http.Request) {
    var employees []Employee
    employees = GetEmployees()
    log.Println(employees)
    template_html.ExecuteTemplate(writer, "Home", employees)
}

func Create(writer http.ResponseWriter, request *http.Request) {
    template_html.ExecuteTemplate(writer, "Create", nil)
}
func Insert(writer http.ResponseWriter, request *http.Request) {
    var employee Employee 
    employee.EmployeeName = request.FormValue("name")
    employee.SSN = request.FormValue("ssn")
    employee.Designation = request.FormValue("designation")
employee.Department =
request.FormValue("department")
InsertEmployee(employee)
var employees []Employee
employees = GetEmployees()
template_html.ExecuteTemplate(writer, "Home",
employees)
}
func Alter(writer http.ResponseWriter, request
*http.Request) {
var employee Employee
var employeeId int
var employeeIdStr string
employeeIdStr = request.FormValue("id")
fmt.Sscanf(employeeIdStr, "%d", &employeeId)
employee.EmployeeId = employeeId
employee.EmployeeName =
request.FormValue("name")
employee.SSN = request.FormValue("ssn")
employee.Designation =
request.FormValue("designation")
employee.Department =
request.FormValue("department")
UpdateEmployee(employee)
var employees []Employee
employees = GetEmployees()
template_html.ExecuteTemplate(writer, "Home",
employees)
}
func Update(writer http.ResponseWriter, request
*http.Request) {
var employeeId int
var employeeIdStr string
employeeIdStr = request.FormValue("id")
fmt.Sscanf(employeeIdStr, "%d", &employeeId)
var employee Employee
employee = GetEmployeeById(employeeId)
template_html.ExecuteTemplate(writer, "Update",
employee)
}
func Delete(writer http.ResponseWriter, request
*http.Request) {
var employeeId int
var employeeIdStr string
employeeIdStr = request.FormValue("id")
fmt.Sscanf(employeeIdStr, "%d", &employeeId)
var employee Employee
employee = GetEmployeeById(employeeId)
DeleteEmployee(employee)
var employees []Employee
employees = GetEmployees()
template_html.ExecuteTemplate(writer, "Home",
employees)
}
func View(writer http.ResponseWriter, request
*http.Request) {
var employeeId int
var employeeIdStr string
employeeIdStr = request.FormValue("id")
fmt.Sscanf(employeeIdStr, "%d", &employeeId)
var employee Employee
employee = GetEmployeeById(employeeId)
fmt.Println(employee)
var employees []Employee
employees = []Employee{employee}
template_html.ExecuteTemplate(writer, "View",
employees)
}
func main() {
log.Println("Server started on:
http://localhost:8000")
http.HandleFunc("/", Home)
http.HandleFunc("/alter", Alter)
http.HandleFunc("/create", Create)
http.HandleFunc("/update", Update)
http.HandleFunc("/view", View)
http.HandleFunc(“/insert", Insert)
http.HandleFunc(“/delete", Delete)
http.ListenAndServe(":8000", nil)
}
```

To compile and run the above golang program, ensure that you have
the latest golang executable. You can download it from
https://golang.org/dl/. Based on the operating system you use, you
can download the appropriate executable.


You can verify the installation by running the following command:
go version
The following is the directory structure of the Go web app based on
web forms. Let us look at the. Directory structure and the files in the
directory:
Figure 5.2: Go web app directory structure
Now, let us look at the backend code for updating the MySQL
database. The input comes from the webforms, which are submitted
through forms. The form actions are mapped to different database
operations. You can see the database querying and updating the
following code:
emp_mgmt_database_operations.go
Getting database connection
To get the database connection, follow this code:
type Employee struct {
EmployeeId   int
EmployeeName string
SSN          string
Designation  string
Department   string
}
func GetConnection() (database *sql.DB) {
databaseDriver := "mysql"
databaseUser := "newuser"
databasePass := "newuser"
databaseName := "empMgmt"
database, error := sql.Open(databaseDriver,
databaseUser+":"+databasePass+"@/"+databaseName)
if error != nil {
panic(error.Error())
}
return database
}
Retrieving employee from the given database Id
Follow this code to retrieve the employee from the given database Id:
func GetEmployeeById(employeeId int) Employee {
var database *sql.DB
database = GetConnection()
var error error
var rows *sql.Rows
rows, error = database.Query("SELECT * FROM
Employee WHERE EmployeeId=?", employeeId)
if error != nil {
panic(error.Error())
}
var employee Employee
employee = Employee{}
for rows.Next() {
var employeeId int
var employeeName string
var SSN string
var Designation string
var Department string
error = rows.Scan(&employeeId,
&employeeName, &SSN, &Designation, &Department)
if error != nil {
panic(error.Error())
}
employee.EmployeeId = employeeId
employee.EmployeeName = employeeName
employee.SSN = SSN
employee.Designation = Designation
employee.Department = Department
}
defer database.Close()
return employee
}
Retrieving all employees from the database
This is how you can retrieve all employees from the database:
func GetEmployees() []Employee {
var database *sql.DB
database = GetConnection()
var error error
var rows *sql.Rows
rows, error = database.Query("SELECT * FROM
Employee ORDER BY Employeeid DESC")
if error != nil {
panic(error.Error())
}
var employee Employee
employee = Employee{}
var employees []Employee
employees = []Employee{}
for rows.Next() {
var employeeId int
var employeeName string
var ssn string
var designation string
var department string
error = rows.Scan(&employeeId,
&employeeName, &ssn, &designation, &department)
if error != nil {
panic(error.Error())
}
employee.EmployeeId = employeeId
employee.EmployeeName = employeeName
employee.SSN = ssn
employee.Designation = designation
employee.Department = department
employees = append(employees, employee)
}
defer database.Close()
return employees
}
Adding employee to the database
You can add employee to the database by following this code:
func InsertEmployee(employee Employee) {
var database *sql.DB
database = GetConnection()
var error error
var insert *sql.Stmt
insert, error = database.Prepare("INSERT INTO
EMPLOYEE(EmployeeName,SSN,Designation,Department)
VALUES(?,?,?,?)")
if error != nil {
panic(error.Error())
}
insert.Exec(employee.EmployeeName, employee.SSN,
employee.Designation, employee.Department)
defer database.Close()
}
Updating the employee in the database
To update the employee in the database, follow this code:
func UpdateEmployee(employee Employee) {
var database *sql.DB
database = GetConnection()
var error error
var update *sql.Stmt
update, error = database.Prepare("UPDATE EMPLOYEE
SET EmployeeName=?, SSN=?,DESIGNATION=?,DEPARTMENT=? WHERE
EmployeeId=?")
if error != nil {
panic(error.Error())
}
update.Exec(employee.EmployeeName, employee.SSN,
employee.Designation, employee.Department,
employee.EmployeeId)
defer database.Close()
}
Deleting the employee from the database
You can delete the employee from the database by following this code:
func DeleteEmployee(employee Employee) {
var database *sql.DB
database = GetConnection()
var error error
var delete *sql.Stmt
delete, error = database.Prepare("DELETE FROM
Employee WHERE Employeeid=?")
if error != nil {
panic(error.Error())
}
delete.Exec(employee.EmployeeId)
defer database.Close()
}
The output will be based on the version that you install. It will be as
below:
bhagvanarch@Bhagvans-MacBook-Air code % go version
go version go1.22.1 darwin/arm64
bhagvanarch@Bhagvans-MacBook-Air code %
To execute the code above, we need a database like MySQL. You need
to set up a database on MySQL. It can be downloaded from
https://dev.mysql.com/downloads/mysql/ based on the operating
system. After installing, you can start the MySQL service.


1. Database user newuser needs to be created with newuser
password.


2. You can import the below SQL after creating a database by name
empMgmt:
CREATE DATABASE `empMgmt` /*!40100 DEFAULT CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci */;
CREATE TABLE `EMPLOYEE` (
`EMPLOYEEID` int(11) NOT NULL AUTO_INCREMENT,
`EMPLOYEENAME` varchar(45) DEFAULT NULL,
`SSN` varchar(45) DEFAULT NULL,
`DESIGNATION` varchar(45) DEFAULT NULL,
`DEPARTMENT` varchar(45) DEFAULT NULL,
PRIMARY KEY (`EMPLOYEEID`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT
CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
3. After the database is setup, you can add your style sheets and
titles in .tmpl files. Let us first look at Home.tmpl:
{{ define "Home" }}
{{ template "Header" }}
{{ template "Menu"  }}
<h2> Employees </h2>
<table border="1">
<thead>
<tr>
<td>Employee ID</td>
<td>Employee Name</td>
<td>SSN</td>
<td>Designation</td>
<td>Department</td>
<td>View</td>
<td>Update</td>
<td>Delete</td>
</tr>
</thead>
<tbody>
{{ range . }}
<tr>
<td>{{ .EmployeeId }}</td>
<td> {{ .EmployeeName }} </td>
<td>{{ .SSN }} </td>
<td>{{ .Designation }} </td>
<td>{{ .Department }} </td>
<td><a href="/view?id={{ .EmployeeId }}
">View</a></td>
<td><a href="/update?id={{ .EmployeeId }}
">Update</a></td>
<td><a href="/delete?id={{ .EmployeeId }} "
onclick="return confirm('Are you sure you want to
delete ?');">Delete</a><td>
</tr>
{{ end }}
</tbody>
</table>
{{ template "Footer" }}
{{ end }}
4. After verifying the installation of the Go compiler, you can
compile and run the code using the following commands:
go mod init webforms
go mod tidy
The output will be as follows:
Figure 5.3: Go web app start output
5. You can access the web application at http://localhost:8000/.


The following figure shows the landing page:
Figure 5.4: Landing page for Employee Management
6. You can access the create employee form by clicking on the link
above named—create employee as shown in the following figure:
Figure 5.5: Create Employee web form
7. The above-created form is based on the Create.tmpl. The create
template file is shown as follows:
Create.tmpl
{{ define "Create" }}
{{ template "Header" }}
{{ template "Menu"  }}
<br>
<h1>Create Employee</h1>
<br>
<br>
<form method="post" action="/insert">
Employee Name: <input type="text" name="name"
placeholder="name" autofocus/>
<br>
<br>
SSN: <input type="text" name="ssn"
placeholder="ssn"/>
<br>
<br>
Designation: <input type="text" name="designation"
placeholder="designation"/>
<br>
<br>
Department: <input type="text" name="department"
placeholder="department"/>
<br>
<br>
<input type="submit" value="Create Employee"/>
</form>
{{ template "Footer" }}
{{ end }}
After the creation of employees, you can see the output on the
home screen as follows:
Figure 5.6: List of Employees Table
8. The above view form is based on Home.tmpl. You can update the
employee’s name, ssn, designation, or department by clicking
on the update link as shown in the following figure:
Figure 5.7: Update Employee Form
9. The Update form shown above is based on Update.tmpl. The
update template file is shown as follows:
Update.tmpl
{{ define "Update" }}
{{ template "Header" }}
{{ template “Menu"  }}
<br>
<h1>Update Employee</h1>
<br>
<br>
<form method="post" action="/alter">
<input type="hidden" name="id" value="{{
.EmployeeId }}" />
Employee Name: <input type="text" name="name"
placeholder="name" value="{{ .EmployeeName }}"
autofocus>
<br>
<br>
SSN: <input type="text" name="ssn" value="{{ .SSN
}}" placeholder="ssn"/>
<br>
<br>
<br>
<br>
Designation: <input type="text" name="designation"
value="{{ .Designation }}" placeholder="designation"/>
<br>
<br>
Department: <input type="text" name="department"
value="{{ .Department }}" placeholder="department"/>
<br>
<br>
<input type="submit" value="Update Employee"/>
</form>
{{ template "Footer" }}
{{ end }}
10. You can delete the employee’s record by clicking on the delete
link from the home screen, as shown in the following figure:
Figure 5.8: Delete Employee—Confirm dialog
11. You can see the updated list of employee records in the following
figure:
Figure 5.9: Updated list of employees
Note: In most software projects, web-based applications are popular. Now-a-
days progressive web applications are being built to have web and mobile
web applications. These web applications talk to the backend relational
database like MySQL.


Developing web app using Gin and REST API
In this section, we will look at building web applications using the Gin
web framework. REST API can be developed using Gin and Vue.js,
which can be consumed and bound to REST services built on Gin.


1. First, let us look at the REST API routes:
Action
Http
method
URL route
Description
GetEvent
GET
/api/events/:index
Retrieving an event
AddEventToList
POST
/api/events/
Adding an Event to
the list
GetEventList
GET
/api/events
Retrieving the list of
events
Table 5.1: API routes
Now, let us look at the code to implement various REST API
routes:
main.go
package main
import (
"github.com/gin-contrib/static"
"github.com/gin-gonic/gin"
"net/http"
"strconv"
)
var gevents []string
func Events(c *gin.Context) {
c.JSON(http.StatusOK, gin.H{"list": gevents})
}
func GetEventList(c *gin.Context) {
errormessage := "Index out of range"
indexstring := c.Param("index")
if index, err := strconv.Atoi(indexstring); err
== nil && index < len(gevents) {
c.JSON(http.StatusOK, gin.H{"item":
gevents[index]})
} else {
if err != nil {
errormessage = "Number
expected: " + indexstring
}
c.JSON(http.StatusBadRequest,
gin.H{“error": errormessage})
}
}
func AddEventToList(c *gin.Context) {
item := c.PostForm("item")
gevents = append(gevents, item)
c.String(http.StatusCreated,
c.FullPath()+"/"+strconv.Itoa(len(gevents)-1))
}
func main() {
gevents = append(gevents, "Go For a Walk : 8
A.M.")
r := gin.Default()
r.Use(static.Serve("/",
static.LocalFile("./eventsmgr/dist", false)))
r.GET("/api/events", Events)
r.GET("/api/events/:index", GetEventList)
r.POST("/api/events", AddEventToList)
r.Run()
}
2. In the following figure, you can see the directory structure for the
web application based on Gin:
Figure 5.10: Web App Directory structure
Now, let us look at the Vue.js consuming the REST API:
Action
Http
Method
URL Route
React
Method
Description
GetEvent
GET
/api/events/:index
Retrieving
an event
AddEventToList
POST
/api/events/
sendItem()
Sending an
event to
the list
GetEventList
GET
/api/events
getList()
Retrieving
the list of
events.


Table 5.2: URL routes mapped to actions
Let us look at the template which renders the web form. The code
to be used for the form is:
App.vue
<template>
<div id="app">
<h1>Event List</h1>
<div id="widget-container"></div>
<form @submit.prevent="sendItem()">
<input
type="text"
size="50"
v-model="geventitem"
placeholder="Enter new item"
/>
<input type="submit" value="Submit" />
</form>
<ul>
<li v-for="item in geventlist" v-
bind:key="item">{{ item }}</li>
</ul>
<div>{{ message }}</div>
</div>
</template>
<script>
import axios from "axios";
const appData = {
geventlist: ["Evening Party : 6 p.m."],
token: "",
message: "",
};
export default {
name: "App",
data() {
return appData;
},
methods: {
getList: getList,
sendItem: sendItem,
},
mounted: function () {
getList();
},
};
function getList() {
axios.get("/api/events").then((res) => {
appData.geventlist = res.data.list;
});
}
async function sendItem() {
const params = new URLSearchParams();
params.append("item", this.geventitem);
await axios
.post("/api/events", params)
.then(function () {
getList();
})
.catch(function (error) {
appData.geventlist = [error.message];
});
}
</script>
<style>
#app {
font-family: Avenir, Helvetica, Arial, sans-serif;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
text-align: center;
color: #2c3e50;
margin-top: 60px;
}
</style>
3. Now, let us look at the JavaScript code which renders the Vue.js
app:
main.js
import Vue from 'vue'
import App from './App.vue'
Vue.config.productionTip = false
new Vue({
render: h => h(App),
}).$mount('#app')
4. index.html is the html page for displaying the app. main.js is
the JavaScript file which creates the html for the application
based Vue.js.


index.html
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible"
content="IE=edge">
<meta name="viewport" content="width=device-
width,initial-scale=1.0">
<link rel="icon" href="<%= BASE_URL %>favicon.ico">
<title><%= htmlWebpackPlugin.options.title %>
</title>
</head>
<body>
<noscript>
<strong>We're sorry but <%=
htmlWebpackPlugin.options.title %> doesn't work
properly without JavaScript enabled. Please enable it
to continue.</strong>
</noscript>
<div id="app"></div>
<!-- built files will be auto injected -->
</body>
</html>
5. You can now compile and run the Vue.js application. The
package.json has the required modules to be installed. It is
shown below:
{
"name": "eventsmgr",
"version": "0.1.0",
"private": true,
"scripts": {
"serve": "vue-cli-service serve",
"build": "vue-cli-service build",
"lint": "vue-cli-service lint"
},
"dependencies": {
"@okta/okta-auth-js": "^4.0.3",
"@okta/okta-signin-widget": "^4.5.2",
"@okta/okta-vue": "^2.1.1",
"axios": "^0.21.0",
"core-js": "^3.6.5",
"vue": "^2.6.11"
},
"devDependencies": {
"@vue/cli-plugin-babel": "~4.5.0",
"@vue/cli-plugin-eslint": "~4.5.0",
"@vue/cli-service": "~4.5.0",
"babel-eslint": "^10.1.0",
"eslint": "^6.7.2",
"eslint-plugin-vue": "^6.2.2",
"vue-template-compiler": "^2.6.11"
},
"_id": "todo-vue@0.1.0",
"readme": "ERROR: No README data found!"
}
6. The commands are shown below:
cd eventsmgr
npm clean-install
npm install
npm run build
cd..


go run main.go
The output will be as follows:
Figure 5.11: Vue.js web app start output
In the node_modules, you can see the list of modules installed.


Figure 5.12: Node_modules
The following screenshots show how the application renders, and how
the users can interact with the application.


You can access the Vue.js app at http://localhost:8080/
Figure 5.13: Event Creation Web Form
Adding an Event to the Event List through the Vue.js app is
displayed as follows:
Figure 5.14: Updated Event List
Viewing the updated Event List is shown as follows:
Figure 5.15: Updated Event List after adding two events
Viewing All Events after the addition of events is shown as
follows:
Figure 5.16: Updated Event List after adding 3 events
Developing Web App using Beego
In Go, you can use Beego web framework to develop web applications.


Beego is an opensource framework that has application tools, ORM
framework, and other packages. ORM framework helps in cutting down
the ordeal of developing the relational schema and mapping it to
object classes and names of the attributes. Beego is used to build
REST APIs, which can be integrated with opensource JavaScript
frontend frameworks. We can build Beego web application using the
Beego and SQLite database. Beego framework helps in developing
web apps at affordable cost.


1. First, let us look at the code for Beego web application:
main.go
package main
import (
“github.com/astaxie/beego"
“github.com/astaxie/beego/orm"
_ "github.com/mattn/go-sqlite3"
models "go_web_app_beego/models"
_ "go_web_app_beego/routers"
)
func init() {
orm.RegisterDriver("sqlite", orm.DRSqlite)
orm.RegisterDataBase("default", "sqlite3",
"database/products.db")
orm.RegisterModel(new(models.Product))
}
func main() {
beego.Run()
}
The following screenshot shows the Beego Web app directory
structure:
Figure 5.17: Web App Directory structure
The above file main.go has the code for initialization of ORM and
registration of the model. The Beego framework is started in the
main method.


2. Now, let us look at the file Models.go.


Models.go
package models
type Product struct {
Id       int    `form:"-"`
Name     string `form:"name,text,name:"
valid:"MinSize(5);MaxSize(20)"`
Category string
`form:"category,text,category:"`
Image    string `form:"image,text,image:"`
}
func (a *Product) TableName() string {
return "products"
}
Models.go has the code for tableName definition and the object
class specification.


3. Further, let us look at the database script to create a table in the
SQLite database:
Database
CREATE TABLE "products" (
"id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
"name" varchar(200) NOT NULL,
"category" varchar(100),
"image" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
4. The following code has routers configured that map to the Beego
web application controller and its methods:
Routes.go
package routers
import (
"github.com/astaxie/beego"
"go_web_app_beego/controllers"
)
func init() {
beego.Router("/",
&controllers.MainController{})
beego.Router("/webapp/:id([0-9]+)",
&controllers.MainController{}, "get:BeegoWebApp")
beego.Router("/manage/add",
&controllers.ManageController{}, "get,post:Add")
beego.Router("/manage/view",
&controllers.ManageController{}, "get:View")
beego.Router("/manage/home",
&controllers.ManageController{}, "*:Home")
beego.Router("/manage/delete/:id([0-9]+)",
&controllers.ManageController{}, "*:Delete")
beego.Router("/manage/update/:id([0-9]+)",
&controllers.ManageController{}, "*:Update")
}
The following table shows the routes/paths mentioned in
Routers.go:
Action
Controller
Method
Http
Method
URL Route
Retrieve
get,post:Add
GET
/manage/add
Get
get:View
GET
/manage/view
Get All
Home
GET
/manage/home
Delete
Delete
DELETE
/manage/delete/:id([0-9]+
Update
Update
PUT
"/manage/update/:id([0-
9]+
Table 5.3: Actions mapped to URL routes
5. Now, let us look at the file manage.go.


manage.go
manage.go has various sections related to different pages. Let us
start with the Homepage:
Home Page
package controllers
import (
"fmt"
"github.com/astaxie/beego"
"github.com/astaxie/beego/orm"
"github.com/astaxie/beego/validation"
"go_web_app_beego/models"
"strconv"
)
type ManageController struct {
beego.Controller
}
func (manage *ManageController) Home() {
manage.Layout = "basic-layout.tpl"
manage.LayoutSections = make(map[string]string)
manage.LayoutSections["Header"] = "header.tpl"
manage.LayoutSections["Footer"] = "footer.tpl"
manage.TplName = "manage/home.tpl"
}
Now, let us see the Delete Page section in manage.go:
Delete Page
func (manage *ManageController) Delete() {
manage.Layout = "basic-layout.tpl"
manage.LayoutSections = make(map[string]string)
manage.LayoutSections["Header"] = "header.tpl"
manage.LayoutSections["Footer"] = "footer.tpl"
manage.TplName = "manage/home.tpl"
productId, _ :=
strconv.Atoi(manage.Ctx.Input.Param(":id"))
ormGorm := orm.NewOrm()
ormGorm.Using("default")
product := models.Product{}
if exist :=
ormGorm.QueryTable(product.TableName()).Filter("Id",
productId).Exist(); exist {
if num, err :=
ormGorm.Delete(&models.Product{Id: productId}); err ==
nil {
beego.Info("Record Deleted. ",
num)
} else {
beego.Error("Record couldn't be
deleted. Reason: ", err)
}
} else {
beego.Info("Record Doesn't exist.")
}
}
Now, let us see the Update Page Controller section in manage.go:
Update Web Page – Controller
func (manage *ManageController) Update() {
ormGorm := orm.NewOrm()
ormGorm.Using("default")
flash := beego.NewFlash()
if productId, err :=
strconv.Atoi(manage.Ctx.Input.Param(":id")); err == nil
{
product := models.Product{Id:
productId}
if ormGorm.Read(&product) == nil {
product.Category = "Retail"
product.Image = "Bag.jpg"
if num, err :=
ormGorm.Update(&product); err == nil {
flash.Notice("Record
Was Updated.")
flash.Store(&manage.Controller)
beego.Info("Record Was
Updated. ", num)
}
} else {
flash.Notice("Record Was NOT
Updated.")
flash.Store(&manage.Controller)
beego.Error("Couldn't find
product matching id: ", productId)
}
} else {
flash.Notice("Record Was NOT Updated.")
flash.Store(&manage.Controller)
beego.Error("Couldn't convert id from a
string to a number. ", err)
}
manage.Redirect("/manage/view", 302)
}
Now, let us see the View Web Page Controller section in
manage.go:
View Web Page - Controller
func (manage *ManageController) View() {
manage.Layout = "basic-layout.tpl"
manage.LayoutSections = make(map[string]string)
manage.LayoutSections["Header"] = "header.tpl"
manage.LayoutSections["Footer"] = "footer.tpl"
manage.TplName = "manage/view.tpl"
flash :=
beego.ReadFromRequest(&manage.Controller)
if ok := flash.Data["error"]; ok != "" {
manage.Data["errors"] = ok
}
if ok := flash.Data["notice"]; ok != "" {
manage.Data["notices"] = ok
}
ormGorm := orm.NewOrm()
ormGorm.Using("default")
var products []*models.Product
num, err :=
ormGorm.QueryTable("products").All(&products)
if err != orm.ErrNoRows && num > 0 {
manage.Data["records"] = products
}
}
Now, let us see the Add Page Controller section in manage.go:
Add Web page - Controller
func (manage *ManageController) Add() {
manage.Data["Form"] = &models.Product{}
manage.Layout = "basic-layout.tpl"
manage.LayoutSections = make(map[string]string)
manage.LayoutSections["Header"] = "header.tpl"
manage.LayoutSections["Footer"] = "footer.tpl"
manage.TplName = "manage/add.tpl"
flash :=
beego.ReadFromRequest(&manage.Controller)
if ok := flash.Data["error"]; ok != "" {
manage.Data["flash"] = ok
}
ormGorm := orm.NewOrm()
ormGorm.Using("default")
product := models.Product{}
if err := manage.ParseForm(&product); err !=
nil {
beego.Error("Couldn't parse the form.


Reason: ", err)
} else {
manage.Data["Products"] = product
valid := validation.Validation{}
isValid, _ := valid.Valid(product)
if manage.Ctx.Input.Method() == "POST"
{
if !isValid {
manage.Data["Errors"] =
valid.ErrorsMap
beego.Error("Form
didn't validate.")
} else {
searchProduct :=
models.Product{Name: product.Name}
beego.Debug("Product
name supplied:", product.Name)
err =
ormGorm.Read(&searchProduct)
beego.Debug("Err:",
err)
flash :=
beego.NewFlash()
if err == orm.ErrNoRows
|| err == orm.ErrMissPK {
beego.Debug("No
product found matching details supplied. Attempting to
insert product: ", product)
id, error :=
ormGorm.Insert(&product)
if error == nil
{
msg :=
fmt.Sprintf("Product inserted with id:", id)
beego.Debug(msg)
flash.Notice(msg)
flash.Store(&manage.Controller)
} else {
msg :=
fmt.Sprintf("Couldn't insert new product. Reason: ",
err)
beego.Debug(msg)
flash.Error(msg)
flash.Store(&manage.Controller)
}
} else {
beego.Debug("Product found matching details supplied.


Cannot insert")
}
}
}
}
}
6. Now, let us look at the Home.tpl web page.


Home.tpl
<div class="container">
<div class="row">
<div class="hero-text">
<h1>Portal Home</h1>
<p class="lead">This portal is used for managing
products</p>
</div>
</div>
</div>
</div>
You can create this template in HTML in the folder managed
below views. HTML template has the forms and the details of the
table required for rendering in html. It is shown in the following
figure:
Figure 5.18: Location of HTML templates
7. You can now compile and run the main.go. The command to
create a module go_web_app_beego is:
go mod init go_web_app_beego
go mod tidy
8. Then, the module is built using this command:
go build
9. The output will be as follows:
Figure 5.19: Beego Web App Build and Starting output
10. You can access the Beego web app at http://localhost:8080/.


The following figure shows the landing page for adding a product:
Figure 5.20: Add A Product Web Form
a. The Products List page is shown as follows:
Figure 5.21: List of Products Web Page
b. The Product Details page is shown in the following figure:
Figure 5.22: Product Details Web Form
c. The Products List page shows the added products in the
previous steps, as shown in the following figure:
Figure 5.23: Updated List of Products Web Page
Conclusion
In this chapter, we have covered topics related to Go Web Applications
using Web Forms, Gin, and Beego. Examples were presented to
demonstrate where web applications can be built using REST API using
Vue.js, WebForms, and Beego Framework.


In the next chapter, we will learn how to build RPC apps using Go.


These apps are built using Google gRPC. We will also learn about
protocol buffers. Further, gRPC clients will be developed to talk to the
gRPC server. Also, Unit tests will be written to test the clients and
server code.


Points to remember
Web applications can be built using Go, web forms, and
database-specific packages in Go.


REST API can be developed using Gin. ORM-based backend can
be developed using GORM. Web applications can be built by
having Vue.js views talking to REST API using Gin.


Beego Framework has capabilities to have REST APIs, ORM, and
developing web application framework.

