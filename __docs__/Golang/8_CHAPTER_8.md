# 8 CHAPTER 8

Go Concurrent Apps
Introduction
In this chapter, readers will learn how to build concurrent
applications using Go, as well as how to use go routines,
channels, and concurrency. They will also understand the
principles of concurrency and parallelism. Now, let us look at
the Go Lang principles and concurrent apps:
Figure 8.1: Go concurrent apps
Structure
The chapter covers the following topics:
Concurrency and parallelism principles
Goroutines
Channels
Concurrent apps in Go
Objectives
By the end of the chapter, the reader will learn to build
concurrent applications in the Go language. They will also be
presented with various examples showing the go routines
and how to implement concurrency. The reader will learn
concurrency and parallelism principles and real-life examples
where concurrency is important. The reader will see the
difference between concurrency and parallelism. We will see
some examples in Go and implementations of concurrency
principles.


Concurrency and parallelism principles
Recapping from the second chapter, Concurrency differs
from parallelism in that concurrent tasks need to end at the
same time and start at the same time. On the other hand,
single-threaded tasks get executed one by one, like code
statements. Now, let us look at concurrency in real life,
where multiple tasks are concurrent in nature:
Figure 8.2: Concurrent tasks
Let us first look at a layman’s example. On the road, there
will be vehicles going on parallel roads in different directions.


If there are two lanes on the road, you can have vehicles
concurrently move in the same direction on multiple lanes.


Figure 8.3 illustrates the same:
Figure 8.3: Layman example
In your daily life, you can see the above happening on the
road while traveling. The same applies to multilane airports
or train stations with multiple platforms, as explained in the
following figure:
Figure 8.4: Daily life example—Concurrency
Like the road and lanes example, there are instances where
concurrency and parallelism principles are applied.


Concurrent apps have tasks executed simultaneously. These
tasks can be related to app loading, app rendering, and
media rendering and loading.


In the Go language, concurrency features are available out of
the box. First, let us look at concurrency vs. parallelism. Now,
let us look at an example where parallel tasks are executed
instantly and see the difference between concurrency and
parallelism:
Figure 8.5: Parallelism vs. concurrency
You can see this happening in the browser when you click on
the website and download an executable simultaneously.


Rendering, downloading, asynchronous processing of
requests, rendering asynchronous responses, and loading of
the browser panels happen simultaneously. This is a good
example of concurrent applications. Parallelism can be
observed in big data problems where batches of 1 k are
analyzed in a dataset of 1 gigabyte. Multiple threads are
spanned to work on 1k each, and the overall computation
time comes down as they are parallelized.


Now, let us look at a company where employees are working
on their specific departmental tasks (Figure 8.6):
Figure 8.6: Concurrency in a company
In an organization, concurrent operations can occur in
various departments, such as marketing, sales, logistics,
accounting, finance, and HR. Employees create many
documents, and printouts are taken from the printers
available. Concurrent tasks of creating documents and
parallelly generating printouts happen. Each employee
focuses on a documenting task and stops before taking a
printout. The employee can work on another document while
printing the document. All these activities happen
simultaneously.


After looking at different concurrency and parallelism
principles, you can understand how we can simulate the
same using Golang go routines. We will talk about go
routines in detail in the next section. While building an
application, you need to start looking at the concurrent and
parallel tasks that can be performed by the user. An
application can be concurrent and not parallel. It can be
parallel and not concurrent. Big data application is a good
example. An application can be neither parallel nor
concurrent in nature. The workflow-based application is
where tasks are executed in sequence, and there is a wait
time for task completion. There can be applications that can
be both concurrent and parallel. Now, let us look at the
assessment matrix where parallelism and concurrency are
relevant:
Figure 8.7: Assessment matrix of parallelization vs. concurrency
Note: Concurrent tasks can be in progress, whereas parallelized tasks
can be executed simultaneously. Concurrency can utilize parallelism
principles to ensure multiple tasks progress simultaneously.


Parallelism is not the only motive for concurrency. Overall processing
time comes down as multiple cores are used for processing during
concurrent task execution. Go Lang has built-in features for
concurrency.


Goroutines
Let us recap from Chapter 2, Advanced Features of Go, about
Goroutines: In Go Lang, the go routine gets executed with a
thread owned by it. Go routines are functions.


Now, let us look at how Goroutines are executed on a
computer. A computer has CPUs and processors associated
with the CPU. An operating system like Unix, Linux, MacOS,
and Windows can spawn processes and execute threads
within the process. Now, let us look at the real-life examples
for Goroutines:
Figure 8.8: Goroutines
A processor manages different threads in a process allocated
to the core. Go routines are associated with threads in an
operation system. Now, let us look at the queuing of
Goroutines when we have multiple cores and CPUs in a
computer. Now, let us look at the example and structure of
the Global run queue and local run queue:
Figure 8.9: Global run queue and local run queue
Operating systems have a scheduler for assigning the
process and threads to a core. CPU1 will manage threads in a
process. The scheduler in Go Lang manages the global
Execution queue of go routines. Go Lang scheduler picks the
o routine to a Local Run queue of an operating system
thread. This scheduler is based on a cooperative model and
is not preemptive. In the cooperative model, threads can
yield explicit execution. This helps in allocating the other go
routine for execution. During the allocation, the context gets
switched across the go routines. This is where the case
scheduler plays an important role. Go Lang Runtime decides
the priority of go routine execution. Runtime takes care of
methods such as invocation, garbage collection, network
communication, channel operations, blocking, and other
events. Context switching happens only in the event of a
change of go routine to be executed.


The following figure shows how the Go Lang Program
interacts with Runtime and OS kernel:
Figure 8.10: Go Binary—Runtime and kernel interaction
Let us see the steps involved in Runtime and kernel
interaction.


1. OS Kernel manages the sys calls and creation of the
threads.


2. Runtime manages the creation of the go routines,
channel communication, and memory allocation.


3. In the group’s scenario, the go routines group can wait
till the other group of go routines is executed.


4. The sync package is used to do a sleep before the shift
of allocation of go routine’s group.


Now, let us look at the sample code for an anonymous go
routine:
anonymous_go_routine.go
package main
import (
"fmt"
"time"
)
func main() {
fmt.Println("Starting the main function")
go func() {
fmt.Println("Calling the anonymous
function")
}()
time.Sleep(1 * time.Second)
fmt.Println("Leaving the main Function")
}
After verifying the installation of the Go compiler, you can
compile and run it using the following commands. The
commands are as follows:
go run anonymous_go_routine.go
The output will be as follows:
Figure 8.11: Output for anonymous go_routine execution
As shown in the code above, the anonymous function does
not have a name. In the program above, you can see the
start of the main function, anonymous function invocation,
and exiting of the main function. Now, let us look at an
example of a go routine.


go_routine_exampe.go
package main
import (
"fmt"
"time"
)
func show(message string) {
for i := 0; i < 7; i++ {
time.Sleep(1 * time.Second)
fmt.Println(message)
}
}
func main() {
go show("go routine")
show("running a method")
}
In the code above, a go routine named show is defined to
show the message. In the main method, the example code
shows the difference between the execution of the goroutine
and just the method.


You can compile and run it using the following commands.


The commands are as follows:
go run go_routine_example.go
The output will be as follows:
Figure 8.12: Output for go_routine example execution
In the above code, you can see the difference and sequence
of execution of the go routine and just the method in Go
Lang.


Channels
Let us recap what we learned in Chapter 2, Advanced
Features of Go, about Channels: Channels provide the
capability or a feature for go routines to share information
with another go routine. The channel can send the data to
another channel. Channels are bidirectional. Channels can be
created by using chan keyword.


Figure 8.13: Channels
In Figure 8.13, you can see how the channel is used for
Routine communication. Concurrent Goroutines can
communicate using channels for the exchange of messages
and data. Channel Operator <- is used in the code for
different channel operations. In a web application,
Goroutines can help improve performance by designing and
developing concurrent tasks as Goroutines.


Figure 8.14: Channel—Send and receive messages
Go routines exchange data and information using channels.


Channel blocks and waits to receive and transmit the data to
the destination go routine. Channel data is typically shared
by iterating in a loop over the data.


Now, let us look at an example where go routines help
improve performance and efficiency. In the case of big data
solutions, jobs or tasks are created through data creation,
processing, querying, persistence, and storage of
information. These different tasks communicate through
channels as batches of data are processed through this
pipeline. Now, let us look at the data engineering process
and the different steps in the process:
Figure 8.15: Data engineering
Big data solutions involve handling data tasks like receiving
data and sending data from the source to the destination.


The challenge is in handling terabytes of data like
transactions, logs, metrics, and batch job updates. Most of
the database driven solutions have steps for staging,
cleansing, processing, and analysis of the data.


Communication is the key to these solutions between the
source and the destination. Where the data is getting
processed and which step in the process is very important
for solutions in an enterprise. Business activity monitoring
and analytics solutions focus on identifying the activities
related to business data and presenting the status of the
pipeline execution.


In enterprises, the challenge is managing the cost vs
efficiency for big data solutions. Most of the time, developers
choose small programs and scripts to handle different steps
in the process. Go lang is used not just for big data solutions
but also for small programs. Data pipelines can be written in
Go Lang programs. Each pipeline has input and output data,
and different steps in the process can be data pipelines. The
concurrency model helps break big data into manageable
chunks and handle data using data pipelines.


An example of using go routines and channels is a Go-lang-
based program for processing customers’ transactions to
update loyalty points for different customers. The loyalty
program has gold, silver, and bronze levels. Each program
has several points to be achieved. Go routines and channels
are used to process the data in batches.


Error handling and exception management are easy in Go
Lang since the transactions that are problematic can moved
into a separate file. At the end of the execution, you can
identify the transactions that are analyzed and others that
are causing errors.


Now, let us start coding a channel. You can look at the
channel example as follows:
channel_example.go
package main
import "fmt"
func main() {
var channel chan int
fmt.Println(" channel's Value: ", channel)
fmt.Printf("channel's Type: %T ", channel)
channel1 := make(chan int)
fmt.Println("\n channel1's Value: ",
channel1)
fmt.Printf("channel1's Type: %T ",
channel1)
}
The sample code above shows the creation of a channel, and
its value and type are printed.


You can now compile and run the channel_example.go using
the following command:
go run channel_example.go
The output will be as follows:
(base) apples-MacBook-Air:src bhagvan.kommadi$ go
run channel_example.go
channel's Value:  <nil>
channel's Type: chan int
channel1's Value:  0xc00005a060
channel1's Type: chan int (base) apples-MacBook-
Air:src bhagvan.kommadi$
Now, we will move to the next example. We will look at the
code for channel messaging, including the send and receive
methods:
channel_example.go
package main
import "fmt"
func exfunc(channel chan int) {
fmt.Println(123 + <-channel)
}
func main() {
fmt.Println("Main method has started")
channel := make(chan int)
go exfunc(channel)
channel <- 23
fmt.Println("Main method has ended")
}
In the above example, channel messaging and
sending/receiving messages from the channel-to-go routine
are shown.


You can now compile and run the channel_messaging.go
using the following command:
go run channel_messaging.go
The output will be as follows:
Figure 8.16: Output for channel_messaging.go execution
Now, let us look at the code for channel messaging; send
and receive methods. We will look at channel_closing.go:
channel_closing.go
package main
import "fmt"
func newfunc(channel chan string) {
for v := 0; v < 4; v++ {
channel <- "running the channel"
}
close(channel)
}
func main() {
channel := make(chan string)
go newfunc(channel)
for {
result, message := <-channel
if message == false {
fmt.Println("Closing the
channel ", message)
break
}
fmt.Println("Opening the channel ",
result, message)
}
}
In the example above, you can see the channel closing
operation.


You can now compile and run the channel_closing.go using
the following command:
go run channel_closing.go
The output will be as follows:
Figure 8.17: Channel closing execution output
Channel types
Channel types can be in multiple directions or only in a
single direction. Channel T stands for multiple directions or
bidirectional channel type. In two directions, channels can
transmit and receive messages. chan <-T stands for send
direction channel type. Only sending the messages is
possible. <-chan T stands for receive direction channel type.


The other types of channels are described in the following
table:
Method
Nil
channel
type
Non-nil
channel type
Closed
channel
type
Not-closed
channel type
Close
panic
succeed to
close
panic
succeed to
close
Send
value
block for
ever
block or
succeed to
send
panic
block or
succeed to
send
Receive
value
block for
ever
block or
succeed to
receive
never block
block or
succeed to
receive
Table 8.1: Channel types
The different channel types shown above are as follows:
Nil channel type
Non-nil channel type
Closed channel type
Not-closed channel type
Channel patterns
Now, let us look at different patterns in implementing
channels. Now, let us look at the broadcast pattern example:
Figure 8.18: BroadCast pattern—Channel implementation
Channels are used to send a single message to different
recipients using a broadcast pattern.


A single input channel receives messages from a sender to
broadcast to more than one output channel. More than one
recipient receives the same message from the output
channels. The broadcast pattern helps ensure the concurrent
sending of messages to different recipients.


Now, let us look at the Fan In Pattern in Go Lang as
illustrated in the following figure:
Figure 8.19: Fan In Pattern in Go Lang
A Fan In Pattern is used to combine different channels into
one output channel. The idea is to gather the concurrent
tasks’ data into one channel. This helps one consumer to
process messages in a combined form.


Now, let us look at the Fan Out Pattern related to channels.


The following figure shows the Fan Out Pattern, where data
coming from a single input channel from various senders is
routed to different recipients on different output channels.


Figure 8.20: Fan Out Pattern in Go Lang
The Fan Out Pattern helps share messages across more than
one recipient. A single function in Go Lang can have an array
of output channels as parameters. This function can return
an input channel, which is the multiplexer. The multiplexer
helps share data and distribute messages to more than one
output channel.


Concurrent apps in Go
Concurrency principles and mechanisms are applied in
console, desktop, and web applications. Go routines and
channels ensure that computation happens concurrently.


When the go routines and channels are used, the
application's performance increases. Performance tools and
profilers improve the application's response. Browser
applications are analyzed using developer tools to capture
performance times.


Now, let us look at building a concurrency application. Web
Links can be loaded to analyze the metadata of the website.


We can create the RSS feed for the website, looking at
various topics and content covered. RSS feeds can be
updated frequently based on the website updates. This
application can be built using Go Lang concurrency
principles.


You can have web links configured based on your
preferences and the topics selected. Web links can be
scanned to generate RSS feeds topic-wise. The application
will have the capability to add or remove the web links from
the topics. New topics can be added for the application with
web links.


RSS feeds can be loaded into the application based on the
topic. Feeds can be stored in a NoSQL database. This
application can be on the cloud serving as a software as a
servic e.


Users can register using social authentication,
google/msn/yahoo/twitter. Users can create a profile and
preferences. Feeds can be stored in the NoSQL database and
can be categorized with topics. They can be tagged with
keywords and made searchable for quick access. Cache
mechanisms can be used to improve the retrieval time of the
master data or user preferences and interests.


Another way to speed up performance is to load user-specific
feeds for a category selected in memory. The user can
browse them quickly. Scalability is an issue for this pattern to
be implemented. Concurrent users can browse the feeds
quickly based on the category selection. Multiple categories
and feeds specific to that category can be loaded in memory.


User profiles can be modified, and demographics are stored
in the NoSQL database. This helps recommend feeds for
users. Users can access correlated feeds read by other users.


Feed tags can be selected, and users can add new tags to
the existing tagged feeds.


Figure 8.19 shows the relationship between user profiles and
the demographics of the users in the system. Typical
demographics of the user gathered are as follows:
Age
Gender
Interests
Preferences
Job/Profession
Designation
Figure 8.21: User profile
A recommendation system can be built as an add-on to the
system to provide new feeds and tags for users. Users will be
eager to come back to the application because new content
will be ready to be processed as recommendations. This can
be like AdSense, where the context of the user can be used
based on the reader’s browsing history of the feeds.


The user satisfaction is dependent on the user profile and
other factors mentioned in the following list:
User profile
Content
Context
Interests
Figure 8.22: Reader satisfaction triangle
Figure 8.20 shows the triangular forces driving user
satisfaction based on user profile, context, and content. The
recommended feeds are evaluated for performance based on
the following factors:
Click through rate
Stay time
Upvotes
Comments
Reposts
Figure 8.23: Recommendation system metrics
Figure 8.21 shows the important metrics to measure the
performance and efficiency of the recommendation system.


The content provided to the user can be in any of the
following formats:
Textual information
Images/posters
Video
A web link
Document
This content can have multiple labels. Different labels for the
user content are shown in the following figure:
Figure 8.24: User label analysis
As shown in Figure 8.22, user interest-specific labels can be
as follows:
Category
Topic
Keyword
Source
Community
Vertical
The user profile-specific labels can be as follows:
Gender
Age
Location
Preferences
Interests
Favorites
Bookmarks
The user behavior-specific labels can be as follows:
Browsing time
Reading time
Staying time
Click through analysis
Label generation can be done by using the following
methods:
Noise-canceling by filtering the content
Penalizing the trends on feeds
Using time decaying and browsing history
Using global bias and click rate
Likes/dislikes/unsubscribes
Now, let us start building this application. The first step is to
look at accessing the links concurrently. Now, let us look at
the concurrency app code:
concurrency_app.go
package main
import (
"fmt"
"net/http"
)
func main() {
channel := make(chan string)
links := []string{
"https://www.golangbot.com",
"https://www.golang.org",
"https://www.changelog.com/gotime",
"http://qvault.io/",
"https://golang.ch/",
"https://gosamples.dev/",
"https://golangcode.com",
"https://appliedgo.net",
}
for _, link := range links {
go accessLink(link, channel)
}
for message := range channel {
fmt.Println(message)
}
}
func accessLink(link string, channel chan string) {
if _, error := http.Get(link); error != nil
{
channel <- link + " is not
accessible"
} else {
channel <- link + " is accessible."
}
}
You can now compile and run the concurrency_app.go using
the following command:
go run concurrency_app.go
The output will be as follows:
Figure 8.25: User label analysis
Conclusion
In this chapter, we have covered topics related to
concurrency and parallelism. Examples were presented to
demonstrate how channels and go routines can be used in
real life.


Concurrency is the superset of parallelism. In real life,
concurrency is observed in various scenarios. Go routines
can process data by having a function executed by the
thread. Channels are used for communication between
multiple go routines. Channel types can be single-directional
or multiple-directional. Channel patterns are broadcast Fan In
and Fan Out Patterns.


In the next chapter, the reader will understand dependency
injection using the Go language. The reader will also
understand SOLID principles, which include dependency
inversion. The reader will know different types of
dependency injection, such as constructor, property, and
method.


Join our book’s Discord space
Join the book's Discord Workspace for Latest updates, Offers,
Tech happenings around the world, New Release and
Sessions with the Authors: <AUTHORS>