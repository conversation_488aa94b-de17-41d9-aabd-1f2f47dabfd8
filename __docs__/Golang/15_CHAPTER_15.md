# 15 CHAPTER 15

Go Performance Tuning
Patterns
Introduction
In this chapter, the readers will learn how to develop Go
applications that are responsive and performant. Different
performance tuning and optimization patterns are presented
with code samples to the readers. The readers will
understand the performance tuning and optimization
techniques while building the web applications. Let us look at
the aspects of Go Lange performance tuning:
Figure 15.1: Go Lang performance tuning patterns
Structure
The chapter covers the following topics:
Go apps performance tuning
Go apps code profiling
Go web apps challenges
Go web apps performance tuning
Go performance patterns
Go scalability patterns
Objectives
In this chapter, we are going to discuss Go Lang performance
tuning patterns. We are going to discuss the challenges and
issues while developing and deploying the web applications
based on Go Lang. We will look into performance tuning and
profiling the Go Lang-based web applications. Different
performance patterns and scalability patterns are discussed
in this chapter.


Go apps performance tuning
Performance tuning in Go (Golang) focuses on optimizing
code for efficiency, memory management, and execution
speed. One of the most important aspects is effective
memory management. In Go, memory is automatically
managed by the garbage collector, but excessive allocations
or improper resource management can lead to performance
degradation. To minimize the memory issues, developers can
use smaller struct sizes, pass large structs by pointer instead
of value, and avoid memory leaks by properly closing
resources like goroutines and file handlers. Reusing objects
through pooling, specifically with sync.Pool, helps reduce
the load on the garbage collector by reusing memory instead
of repeatedly allocating and deallocating the memory.


Another critical area for performance tuning is minimizing
the overhead of garbage collection. Go’s garbage collector
works efficiently for many use cases, but its frequency can
be adjusted using the GOGC environment variable. By tuning
this variable, developers can control the balance between
memory usage and garbage collection frequency. Reducing
heap allocations by reusing objects and limiting pointer use
can also reduce the garbage collector's workload. Escape
analysis is another tool provided by Go, allowing developers
to see where variables escape to the heap and optimize
memory allocation patterns accordingly.


Optimizing the use of goroutines and channels is essential
for high-performance concurrency in Go. While goroutines
are lightweight, they still consume memory and can add
unnecessary overhead if overused or improperly managed.


Developers should ensure that goroutines do not leak by
making sure they exit properly and are not left waiting on
channels or locks indefinitely. Channels should also be
efficiently managed, with appropriate buffer sizes to avoid
bottlenecks. In scenarios where lock contention arises, using
Go's synchronization primitives like sync.Mutex or atomic
operations (sync/atomic) can prevent performance
degradation by reducing the waiting time for shared
resources.


When dealing with I/O operations, performance can be
improved by using buffered I/O, which reduces the number of
system calls and enhances throughput. For tasks involving
big data or frequent I/O, processing in a concurrent manner
using goroutines can help prevent blocking and improve
response times. Along with this, developers should profile
their applications regularly using Go’s built-in tools like
pprof and trace to identify bottlenecks, whether they are in
CPU usage, memory consumption, or goroutine scheduling.


Choosing the right data structures and algorithms is another
way to achieve performance gains. Go’s standard library
provides efficient data structures like slices and maps, but
selecting the most suitable one based on usage patterns can
drastically affect performance. Efficient slicing and reusing
existing slices where possible helps reduce memory
allocations. Moreover, avoiding unnecessary copying of data,
especially when handling large collections, prevents
additional performance hits. In cases where large datasets
are involved, streaming data in chunks rather than loading it
all into memory can help prevent excessive memory
consumption.


Finally, compiler optimizations and Go’s scheduling can be
tuned for performance. The GOMAXPROCS setting, which
determines the number of CPU threads that the Go runtime
can use, should be adjusted based on the number of
available CPU cores to make full use of system resources.


Inlining small functions, which Go’s compiler can do
automatically, reduces the overhead of function calls, while
minimizing the use of reflection, which is slow and costly at
runtime, can help keep the program running efficiently. By
continuously applying these optimization techniques and
regularly profiling the application, developers can ensure
that their Go programs are performant and resource-
efficient.


Go apps code profiling
Code profiling in Go (Go Lang) is a significant technique for
recognizing execution bottlenecks and optimizing asset
utilization. Go gives built-in tools that permit designers, to a
degree, different viewpoints of their applications, such as
CPU utilization, memory allotments, and the behavior of
goroutines. Profiling is basic when an application encounters
execution debasement or versatility issues, as it makes a
difference in pinpointing wasteful aspects that may not be
self-evident from the code alone.


The Go pprof bundle is one of the most effective profiling
apparatuses accessible in the dialect. It empowers engineers
to collect point by point runtime information around CPU and
memory utilization. CPU profiling, for the occasion, tracks the
time that went through on each work, making a difference in
distinguishing parts of the program that devour the most
computational assets. Engineers can utilize CPU profiling to
confine pointlessly moderate or computationally costly
capacities. This data permits them to center their
optimization endeavors on the regions that will give the most
noteworthy execution improvements.


Figure 15.2: Go Lang performance tuning tools
Memory profiling is another key highlight of Go's pprof tool,
giving bits of knowledge into how memory is designated and
utilized over time. It makes a difference in recognizing
memory spills, which happen when memory is distributed
but never discharged, driving to intemperate memory
utilization and possibly slamming the application. Memory
profiles, moreover, uncover designs of over-the-top pile
allotments, which can put weight on the waste collector. By
distinguishing where these assignments are happening,
designers can refactor code to reuse memory more
successfully, decrease rubbish collection overhead, and
make strides in general memory efficiency.


In expansion to prof, Go moreover offers goroutine profiling,
which tracks the behavior and lifecycle of goroutines. Since
goroutines are lightweight strings that empower
concurrency, overseeing them proficiently is fundamental for
ideal execution. Profiling goroutines makes a difference.


Engineers recognize goroutine spills, where unused
goroutines stay in memory or cases, whereas well numerous
goroutines are produced, leading to asset disputes.


Understanding goroutine behavior through profiling permits
for way better concurrency administration, guaranteeing that
the application makes the most proficient utilization of
framework assets without presenting superfluous overhead.


Another instrument worth specifying is Go’s execution tracer,
which gives granular data around program execution,
counting planning occasions, blocking on framework calls,
and goroutine lifecycle occasions. The following yield can
help analyze issues such as intemperate blocking or
destitute utilization of CPU centers. By analyzing this,
designers can fine-tune viewpoints like goroutine planning or
alter the number of strings through the GOMAXPROCS
setting, which controls how numerous CPU centers Go can
utilize concurrently.


To perform profiling in Go, designers regularly use the
net/http/pprof bundle for web-based profiling or utilize the
runtime/pprof bundle for command-line profiling. Once the
profiling information is collected, it can be analyzed utilizing
the go tool pprof command. This instrument creates
reports and visualizations such as fire charts, call charts, and
beat records, making a difference in engineers deciphering
the information and finding issue ranges in their code.


Regular profiling amid the improvement cycle is fundamental
for keeping up ideal execution, particularly as applications
develop and handle more information or activity. By
persistently checking CPU, memory, and goroutine
utilization, designers can capture and settle wasteful aspects
sometime recently that have become major issues,
guaranteeing their Go applications stay versatile and
proficient over time.


Go web apps challenges
Web applications are accessible by desktop and mobile
browsers. The web apps need to be responsive, and users
need to have a good user experience while navigating the
web application. Web applications are developed using best
practices that focus on performance and scalability. They are
unit-tested, and performance testing is done using Go
performance testing frameworks. Performance-critical issues
are found during performance testing. Performance metrics
are gathered for every use case, web pages, and for different
users. The performance test cases cover the scenario where
a total number of users and concurrent users are logged in.


Users need to have quicker web page responses when a total
number of users have logged in and concurrent users are
logged in while accessing the application.


System sizing is done first, and the capacity plan is created
with servers and resources required for the deployment of
the web application. Developers follow the coding best
practices to ensure the code is performant and web pages
are responsive. During code review and performance testing,
the memory issues and concurrency problems are identified.


These issues are resolved to avoid deadlocks and
synchronization problems in the future. The dependencies for
developing web applications are identified, and the latest
versions are used to ensure the problems with older versions
do not exist in the developed web application.


While deploying the web application, multiple servers are
deployed using a load balancer. A load balancer helps send
the traffic and balance the load across the server nodes.


Static web content is separated from the dynamic content.


Static web content is served using web caching servers.


Dynamic web applications are developed using the right web
application framework, which provides scalability and
responsiveness. Web applications need to factor in the
capability of searching the content through search engines.


Web apps need to be designed for network bandwidth
limitations. Based on the users’ network bandwidth, the
pages need to be designed to consume low data from the
backend services.


Go Lang-based web stacks are popular as Go Lang is being
adopted by the developer community. Go Lang was created
by Ken Thompson, Rob Pike, and Robert Griesemer. Go Lang
is the same as C language. Developers like Go Lang over C
as the Go Lang programs are memory-safe, and concurrency
can be implemented easily. There are challenges with Go
Lang in the areas of database services, such as handling
images, managing plugins, balancing load, handling traffic,
managing lesser bandwidth networks for service calls, and
connecting to the network.


Making database calls can be done by having indexes on the
database tables. Complex queries can be analyzed and
improved for performance. The recursive Go Lang method
can create a stack overflow error. Dynamic allocation of
memory to handle big data in memory can cause memory
and performance issues.


Recursion
Let us look at the following code for a recursive program in
Go Lang:
package main
import (
"fmt"
"time"
)
func get_factorial(x int) int {
if x == 0 {
return 1
} else {
return x * get_factorial(x-1)
}
}
func main() {
start_time := time.Now()
factorial := get_factorial(10)
fmt.Println("Factorial of 10 is",
factorial)
elapsed_time := time.Since(start_time)
fmt.Println("Time Taken", elapsed_time)
}
You can now compile and run the recursive_example.go.


The command is as follows:
go run recursive_example.go
The output will be as follows:
Figure 15.3: Recursive example code execution output
In the above example, the factorial of a number is calculated
using recursion. The above example is coded in Go Lang.


Dynamic memory allocation
Now, let us look at the dynamic allocation of memory in Go
Lang:
package main
import (
"fmt"
"runtime"
"time"
)
func dynamicAllocMemory(n int) {
intArray := make([]int, n)
if intArray == nil {
fmt.Println("Unable to create an
Arrray of Size", n)
}
}
func getMemoryStats(memstat runtime.MemStats) {
runtime.ReadMemStats(&memstat)
fmt.Println("memstats.Alloc:",
memstat.Alloc)
fmt.Println("memstats.TotalAlloc:",
memstat.TotalAlloc)
fmt.Println("memstats.HeapAlloc:",
memstat.HeapAlloc)
fmt.Println("memstats.NumGC:",
memstat.NumGC, "\n")
}
func main() {
start_time := time.Now()
var memStat runtime.MemStats
getMemoryStats(memStat)
dynamicAllocMemory(100000000)
elapsed_time := time.Since(start_time)
fmt.Println("Time Taken", elapsed_time)
getMemoryStats(memStat)
}
You can now compile and run the
dynamic_memory_allocation.go. The command is as
follows:
go run dynamic_memory_allocation.go
The output will be as follows:
Figure 15.4: Dynamic memory allocation example code execution output
In the above example, the memory is allocated dynamically
by creating an integer array. The above example is coded in
Go Lang.


HTTP request and response
Let us look at the following code for HTTP request and
response handling in Go Lang:
package main
import (
"fmt"
"net/http"
"runtime"
"time"
)
func invokeHttpCall(url string) {
response, err := http.Get(url)
if err != nil {
return
}
fmt.Println("response body", response)
defer response.Body.Close()
}
func getMemoryStats(memstat runtime.MemStats) {
runtime.ReadMemStats(&memstat)
fmt.Println("memstats.Alloc:",
memstat.Alloc)
fmt.Println("memstats.TotalAlloc:",
memstat.TotalAlloc)
fmt.Println("memstats.HeapAlloc:",
memstat.HeapAlloc)
fmt.Println("memstats.NumGC:",
memstat.NumGC, "\n")
}
func main() {
start_time := time.Now()
var memStat runtime.MemStats
getMemoryStats(memStat)
url := "http://www.amazon.com"
invokeHttpCall(url)
elapsed_time := time.Since(start_time)
fmt.Println("Time Taken", elapsed_time)
getMemoryStats(memStat)
}
You can now compile and run the http_invocation.go. The
command is as follows:
go run http_invocation.go
The output will be as follows:
Figure 15.5: HTTP invocation example code execution output
In the above example, the HTTP request to access a web
page is shown. The above example is coded in Go Lang.


Other challenges
The other challenges in Go Lang-based web applications are
performance issues with unnecessary code, large images,
plugins, and outdated front-end (PHP/JS frameworks)
versions. Unnecessary code can be removed during code
reviews. Images can be compressed, and a content delivery
network can be deployed for image serving. Managing plugin
problems is done by choosing the right plugin and the
version required. Similarly, the front-end user interface
framework needs to be updated regularly to keep up with the
latest versions.


While deploying a web application on a testing environment
before the production environment, performance monitoring
tools must be used to capture the performance metrics and
response times. The environment can be a production
environment replica to ensure the environment capacity
matches the system sizing requirements.


Now, let us look at tuning the performance of the Go Lang
web applications.


Go web apps performance tuning
The key issues observed during performance testing are
related to network connections/DNS, web page loading, code
quality, web traffic handling, and the front-end user interface
frameworks. Many performance testing tools are available
for gathering performance metrics and web page response
times. These tools gather performance metrics, which are as
follows:
Web page response time
System throughput
Request failed—Error rate
Number of concurrent users
Figure 15.6: Go Lang web App Performance Metrics
Performance testing tools that are popular for Go Lang-based
web applications are:
Apache JMeter
Vegeta
Gatling
There are many ways to tune the Go Lang-based web
application performance. Web app performance goals and
requirements need to be identified. These include total
users, concurrent users, web page response times, and
functional metrics like a number of products, categories,
merchants, and others. The system sizing includes these
factors, and the performance testing environment will be the
same as the production environment. During performance
testing, the following system factors are measured:
CPU usage
Memory usage
IO usage
Number of requests
To improve the Go Lang-based web application performance,
caching is used to improve the response time and reduce the
number of calls to the database. Channels and go routines
are used to manage the concurrent requests from different
clients. The database call performance can be improved by
using the ORM Go Lang packages. Performance testing
needs to be done as the functionality gets added to the
system and deployed on production.


Performance and Security are very important aspects of web
applications. Data security is the goal of the security testing.


Risks are related to SQL, scripting, and the deployment
environment resources used. Attacks like SQL injection and
Cross-site scripting need to be avoided by using DevSecOps
tools and executing them early in the software lifecycle. The
best practices suggested are having password policies,
regular software updates, plugin updates, and using best
practices to improve code quality, performance, and security.


Authentication, authorization, encryption, and decryption
strategies are important in the security architecture.


Creating a scalable architecture is important for web
applications. The system can scale as the number of users,
products, categories, centers, sales offices, and production
offices increases. The traffic and load requirements need to
be captured for the next four years to ensure the
architecture can scale after four years. Architecture can be
scalable if the system consists of modules and different
services within the module are scalable and performant.


Deploying the system on the cloud can help the architecture
to have elasticity and load-balancing features of the cloud
environment. Scaling can be done horizontally by having
more servers to handle traffic and load. Vertically, the
architecture can be scaled by having more resources within
the current servers. Monitoring and management of the
application are very important while scaling the application.


The system will be updated with new functionality, and
updates need to be deployed without affecting the system's
performance. Software updates related to plugins,
dependent packages, and environment resources are
important to ensure to close the existing security loopholes.


Web application design needs to factor in that the user
interface needs to be responsive, and the web page needs to
be rendered on mobile phones, tablets, mobile browsers, and
web browsers. The web page rendering needs to factor in
different screen layouts and width/height. The testing
approach needs to handle multiple browsers on different
device types. The application design needs to have a backup
plan to handle old devices and browser versions. To improve
the web application performance, the developer needs to
work on reducing the page response time, web server
response time, and the user experience (UX) of the web
application. Performance best practices need to be
implemented in the code by removing unnecessary code, file
compression, and content separation. content delivery
network (CDN) and caching help in improving web
application performance. Asynchronous loading is another
best practice in improving the performance of the web app.


Content can be loaded by using techniques like lazy loading,
image compression, and minification.


Go performance patterns
Let us now examine the Go performance patterns. First, we
will profile the concurrency handling code.


Concurrency handling
In web applications, different processes need to be executed
concurrently. Ensuring they are performant and profiling the
code is important. The pprof package, which is part of Go
Lang tools, is used to collect CPU and memory metrics. Go
routines and channels-based code can be profiled using the
same tool. Let us look at pprof usage in code:
package main
import (
"fmt"
"net/http"
_ "net/http/pprof"
"time"
)
func executeConcurrentMethod() {
fmt.Println("Executing the concurrent
method")
}
func main() {
go func() {
http.ListenAndServe("localhost:6060", nil)
}()
start_time := time.Now()
go executeConcurrentMethod()
elapsed_time := time.Since(start_time)
fmt.Println("Time Taken", elapsed_time)
time.Sleep(30 * time.Second)
}
You can now compile and run the concurrency_example.go.


The command is as follows:
go run concurrency_example.go
The output will be as follows:
Figure 15.7: HTTP invocation example using pprof code execution output
CPU intensive tasks
Now, we will look into CPU task-intensive code in Go Lang.


Let us look at CPU-intensive task example code:
package main
import (
"fmt"
"time"
)
func executeTask(itr int) int {
output := 0
for cit := 1; cit <= itr; cit++ {
output += cit
}
return output
}
func main() {
cstart_time_taken := time.Now()
coutput := executeTask(1000000)
celapsed_time_taken :=
time.Since(cstart_time_tkaen)
fmt.Println("Time Taken",
celapsed_time_taken)
fmt.Printf("Output is: %d\n", coutput)
}
}
You can now compile and run the cpu_task_example.go.


The command is as follows:
go run cpu_task_example.go
The output will be as follows:
Figure 15.8: CPU task example using pprof code execution output
Web traffic handling
Now, we will look into handling web traffic in Go Lang. The
Go language is strongly typed and close to C in terms of
efficiency. Web traffic can be balanced using load-balancing
strategies like round robin, weighted round robin, least
connections, and IP hash techniques. Balancing traffic using
round-robin involves sending the requests and balancing
them across the web servers. Weighted round-robin factors
in the weights assigned to the web server nodes and their
sizes related to memory, disk space, and CPUs. Least
connections strategies help in sending the request by finding
the current connections used by the server resource. IP hash
strategy helps in mapping the server nodes based on the IP
addresses. Let us look at the WebAppTrafficHandler struct
code:
package main
import (
"fmt"
)
type WebAppTrafficHandler struct {
web_resources []string
index         int
}
func NewWebAppTrafficHandler(webresources []string)
*WebAppTrafficHandler {
return &WebAppTrafficHandler{
web_resources: webresources,
index:         0,
}
}
func (wth *WebAppTrafficHandler)
GetNextWebServerResource() string {
web_resource :=
wth.web_resources[wth.index]
wth.index = (wth.index + 1) %
len(wth.web_resources)
return web_resource
}
func main() {
web_resources := []string{"Resource1",
"Resource2", "Resource3"}
trafficHandler :=
NewWebAppTrafficHandler(web_resources)
for i := 0; i < 10; i++ {
selectedResource :=
trafficHandler.GetNextWebServerResource()
fmt.Println("Request served by:",
selectedResource)
}
}
You can now compile and run the traffic_handling.go.


The command is as follows:
go run traffic_handling.go
The output will be as follows:
Figure 15.9: Traffic handling example using pprof code execution output
Now, let us look at Go scalability and patterns implemented
in practice.


Go scalability patterns
Go web applications need to have the capability to scale
based on the functional requirements. There are different
ways of scaling, such as horizontal and vertical scaling. The
other strategies are stateless design, caching mechanism,
optimizing databases, microservices, and autoscaling (Figure
15.7).


Figure 15.10: Go Lang scalability patterns
A sample code for autoscaling is provided. You need access
to the AWS cloud and credentials. Let us look at the
autoscaling implementation in the code:
package main
import (
"fmt"
"github.com/aws/aws-sdk-go/aws"
"github.com/aws/aws-sdk-go/aws/session"
"github.com/aws/aws-sdk-
go/service/autoscaling"
)
func main() {
web_session :=
session.Must(session.NewSession(&aws.Config{
Region: aws.String("us-west-2"),
}))
web_service := autoscaling.New(web_session)
_, err :=
web_service.CreateAutoScalingGroup(&autoscaling.Cre
ateAutoScalingGroupInput{
AutoScalingGroupName:
aws.String("my-asg"),
LaunchTemplate:
&autoscaling.LaunchTemplateSpecification{
LaunchTemplateName:
aws.String("my-launch-template"),
},
MinSize:         aws.Int64(1),
MaxSize:         aws.Int64(10),
DesiredCapacity: aws.Int64(1),
})
if err != nil {
fmt.Println("Error creating the web
Auto Scaling group:", err)
return
}
_, err =
web_service.PutScalingPolicy(&autoscaling.PutScalin
gPolicyInput{
AutoScalingGroupName:
aws.String("my-asg"),
PolicyName:
aws.String("my-scaling-policy"),
PolicyType:
aws.String("TargetTrackingScaling"),
TargetTrackingConfiguration:
&autoscaling.TargetTrackingConfiguration{
PredefinedMetricSpecification:
&autoscaling.PredefinedMetricSpecification{
PredefinedMetricType:
aws.String("ASGAverageCPUUtilization"),
},
TargetValue:
aws.Float64(70.0),
},
})
if err != nil {
fmt.Println("Error setting up
scaling policy:", err)
return
}
fmt.Println("Web server Auto Scaling group
created and scaling policy set up successfully.")
}
Conclusion
In this chapter, we have covered topics related to Go Web
Application challenges, Go Web Apps Performance Tuning,
Patterns, and Go Lang Web Apps Scalability.


We looked at recursion, dynamic memory allocation, web
request and response handling, and other challenges in the
Go language. Different techniques and approaches were
presented for improving web application’s performance.


Performance patterns were presented with examples related
to concurrency handling, CPU-intensive tasks, and web traffic
handling. We looked at Go Scalability strategies like
horizontal and vertical scaling. The other strategies like
stateless design, caching, autoscaling, and microservices.


Go’s design philosophy prioritizes simplicity, speed, and
efficiency, which leads to high-performing applications that
scale well. From handling concurrent requests in web servers
to managing complex cloud infrastructure, Go’s performance
features make it a preferred language in modern software
engineering. Its low memory footprint, fast execution, and
efficient concurrency model allow developers to build
applications that are both resource-efficient and capable of
handling demanding workloads. In an era where speed and
scalability are key, Go stands out as a language that meets
these needs without sacrificing developer productivity.


In the next chapter, the readers will be presented with web
app blueprint applications. Readers will understand the
design and architecture principles to build scalable web
applications. The blueprint solutions will have the tips and
techniques to build responsive web applications that are
performant.


Join our book’s Discord space
Join the book's Discord Workspace for Latest updates, Offers,
Tech happenings around the world, New Release and
Sessions with the Authors: <AUTHORS>