# 12 CHAPTER 12

Adding Security and IAM
Introduction
In this chapter, the reader will understand basic security
services, advanced security services, security principles, and
secure web applications. The reader will understand how to
add security and identity access management to the
microservices architecture.


Figure 12.1: Microservices security
As shown in the figure above, microservices architecture-
based web applications are built using advanced and basic
security services. All security services are based on the
fundamental security principles.


Structure
The chapter covers the following topics:
Basic security services
Advanced security services
Security principles
Securable web applications
Securing REST API
Objectives
In this chapter, readers will understand how to build basic
and advanced security services. They will also know how to
prevent Cross-Site Request Forgery (CSRF), Denial of
Service (DDOS), and other security attacks. Readers can
also understand the security principles to build scalable,
failproof, resilient, distributed, and secure microservices-
based apps.


Basic security services
To recap, microservices are a group of similar requirements-
based implementations to services. Each service can be
deployed as a package that is independent. Monolithic
applications are not modular but can be deployed as a single
package. Any change impacts the redeployment of the whole
package. The challenges are related to the security of the
independent modules which are deployed as microservices.


The security issues are related to the open loopholes in
security implementation or third-party packages used to
secure the services. Each microservice needs to be protected
by using security controls. Security is important in how the
microservices communicate with each other.


Basic security services like authentication and authorization
need to be implemented first as separate services, which are
the first entry point for any client request. Access control
checks need to be implemented by capturing the details of
who can invoke a particular service. Role-based access
control and attribute-based access controls are popular
security patterns implemented for protecting the
microservices.


Banks, financial services, and insurance companies rely
heavily on information technology in their operations, and as
a result, there is a massive number of users of cryptography
to help guarantee the authenticity, integrity, and
confidentiality of the information ecosystem. Now, let us look
at the user identity management features:
Figure 12.2: User identity management features
Role-based access control is popular in enterprises. User
privileges for different applications need to be managed with
the roles they play in that line of business. Different access
management tools are being adopted and developed by the
enterprise to manage the enterprise's role-based user
privileges. Users' authorization and authentication are based
on the roles that they play in the enterprise and the
particular line of business that the user belongs to. Now, let
us look at the RBAC and the layers in the model:
Figure 12.3: RBAC: Different layers in the model
RBAC-based security models provide various features like
Authentication, Privilege Management, Entitlement
Management, Policy Management, Secrets management,
and identity management. This system can provide
capabilities for self-service, self-registration, and managing
direct entitlements to different applications in the enterprise.


Figure 12.4: RBAC based system features
As shown above, the RBAC system features help in
implementing the security for the microservices over the
above basic security requirements like authentication and
authorization.


Intra-organizational communications sent within the
corporate network or between data centers for information
transfer, backup, and disaster recovery need to be
protected. Typically, these systems are implemented as
hardware or software AES encryption and are vulnerable in
the respect that they use a public key system of key
distribution. Interbank financial messaging across the SWIFT
network is used to transfer payment orders, allowing for
standardized, encrypted transactions between different
banks around the world.


SWIFT operates a public key infrastructure to sign and
encrypt messages sent over SWIFTNet digitally. Crucially,
these messages are based on digital signatures and
encryption algorithms.


Credit card information is protected according to the
Payment Card Industry Data Security Standard
(PCIDSS). Cardholder data is typically encrypted for
transmission, for example, at a point of sale prior to
transmission to the bank. This encryption utilizes symmetric
key cryptography. However, the keys are exchanged using
public key cryptosystems.


Stored data such as tapes and hard disks are encrypted by
organizations for secure offsite archiving. These solutions are
typically based on hardware AES encryption. Online banking
relies upon the TLS protocol to secure web traffic, and server
authentication happens using X.509 certificates, RSA public
keys, and session key establishment.


Now, let us look at advanced security services.


Advanced security services
We have seen microservices communicating through HTTP,
HTTPS, REST, SOAP, and messaging protocols. In the
application architecture based on microservices,
microservices are integrated to work together for high-level
business use. Every microservice can be executed in a
separate container and in its own specific process. To
operate this specific application, we need system admin,
database admin, cloud-specific admin, and API gateway
manager access through IT systems.


We have seen basic security services like authentication and
authorization based on user credentials and roles. Securing
the credentials and having a password policy for expiry and
complexity are important in authentication services. User
lock policy based on retries and ensuring request rate limits
are configured with thresholds to avoid Denial of Service
attacks on the services. For authentication, two factor
authentication can be implemented to improve secure
access by having password and OTP as the methods.


Authentication services need to have a password reset policy
and notification mechanism. SSL-based security mechanisms
can be implemented, and the communication will be based
on HTTPS. Managing the SSL certificates is an important
application management and monitoring administration
procedure.


Microservices are secured by having API keys with expiry and
Access policies like whitelisting IP addresses, secure secret
keys, and private VPN based access. Security testing of the
services is done to prevent SQL injection, cross-site
scripting/forgery, sensitive data loss, failed authentication,
failed authorization, and failed access control. Penetration
security testing helps in avoiding data loss issues and
improves the environment’s integrity. DevSecOps is
important to ensure security while adding new requirements
and deploying new microservices or modifying existing
microservices.


Microservices communication needs to be secured with SSL,
TLS and mTLS protocols. The data over the wire needs to be
protected to avoid tampering of the information and sniffing
of the network packets. Security is important in packaging
the microservices in the container like Docker, Vagrant, and
Kubernetes. The container images need to be monitored and
updated to avoid loopholes related to security.


Application monitoring and management tools like Grafana,
Prometheus, New Relic, Data Dog, and others help in
identifying the loopholes from a security perspective. An
operational plan to react to a problem needs to be in place. A
team needs to focus on root cause analysis of the issue and
identify if it is a security or functionality-related issue. If it is
a security issue, the problem needs to be simplified to
ensure smooth and regular operations. Postmortem of the
issue needs to be conducted to identify the root cause of the
problem and to avoid the same in the future. A security
database needs to be created, and it needs to be updated
regularly for DevSecOps to execute the security checks.


Since microservices can be distributed, security needs to be
addressed at each microservice level, and the deployed
environment needs to be protected. API gateway pattern is
implemented to ensure a single entry point to check the
requests for API Key/credentials/vulnerability issues.


Gateway can be used for enforcing usage policies like
metering, subscription models, and role-based access.


Now, let us look at the security principles that are important
for microservices.


Security principles
Basic security principles like policy-based access, system
integrity, DevSecOps, and shift-left approach are applicable
to microservices architecture. As we discussed,
microservices are independent by design and can be
deployed in a distributed environment. Apps that are
microservice-based architecture have a higher attack surface
and a higher number of services that need to be secured.


The shift-left approach helps in securing microservices
architecture-based applications. Security testing and
performance testing need to be very early in the software
development life cycle. DevSecOps Tools help secure the
code while the microservices are developed, built, and
deployed. The development team and operations work
together in unit testing the software and deploying it after
checking for security issues. Static analysis and dynamic
analysis methods are used for security testing. Static
analysis tools help identify the loopholes in the software and
the dependent packages. Dynamic analysis involves
simulation of the security threat-based attack. Security
testing helps in securing the microservices that operate on
the important information that is sensitive. Information
security best practices are used to protect sensitive data like
credit cards, identity information, bank accounts, etc.;
authentication services and authorization services are
evolving with new methods like multi-factor authentication
and fine-grained access control for authorization.


Different standards have evolved for microservices
architecture, such as SAML, WS-Fed, OpenID, OAuth, OAuth2,
and multi-factor authentication. Declarative authorization
helps define detailed access control for microservices. API
gateways will be the first point of entry for all the services.


Zero trust network defines the services to be requested with
minimum privileges to start with for a client request. All
admin and super user accounts need to be avoided for client
requests. Secrets need to be managed with a secured secret
manager. Secure resources need to be separated from the
zones of authentication and authorization services.


Securable web applications
To secure web applications that are based on microservices-
based architecture, we need to follow the prescribed best
practices for security.


Let us first look at the classic public key infrastructure.


Public key infrastructure
This is used for managing digital certificates. Public key
infrastructure (PKI) provides public and private keys.


Public keys are tough to protect against misuse. PKI
infrastructure in an enterprise needs to protect the private
key and ensure the public keys are available and accessible.


A PKI system consists of a registration authority, a
certification authority, certification management, and private
key management.


One key concern in PKI is How do you ensure that the person
who has the public key belongs to your enterprise.


There is always a threat of man in the middle, such as
sniffing attacks, phishing, and others, which can break the
PKI infrastructure and operations. Certification authorities,
public key certificate management, and registration
authorities need to send updates regularly to ensure identity
is managed in the digital world. Outdated requests or
requests with outdated tokens can be rejected for
authorization and authentication. New quantum algorithms
can help in securing the tokens as it is tough to break the
crypto algorithms. Now, let us look at the different types of
Digital Certificates:
Figure 12.5: Digital Certificates and different types
Digital Certificates can be of different types: Class 1, Class 2,
Class 3, and Class 4. Class 1 is based on the email address of
the requesting user. Class 2 requires more information about
the requested user. Class 3 is issued after user identity is
verified. Class 4 certificates are used by enterprises and
companies. The specifications are as follows:
Class 1: These can be obtained by only providing the
email address.


Class 2: These need more personal information.


Class 3: This first checks the identity of the person
making a request.


Class
4:
They
are
used
by
organizations
and
governments.


PKI provides various features which are useful for enterprises
and companies. Now, let us look at the use cases of PKI:
Figure 12.6: PKI use cases
PKI infrastructure and operations help in providing data
encryption, access rights management, network security
management, digital signing of software, Wi-Fi access
management, and enterprise device identity management.


SSL/TLS certificates
Today, the gaps in SSL/TLS certificates are in network
visibility, automation of Trust Services, and limited use of
Agile best practices. Digital certificates can be deployed as
X.509 certificates that enable the use of popular encryption
algorithms.


SSL/TLS certification authorities provide certification
management and the issuance of new certificates over
expiry. These certificates provide Secure Sockets Layer
security to protect data in transit over the internet. Browsers
are protected using SSL protocol for websites, and users can
safely send private and business data. Transport Layer
Security and Secure Sockets Layer security are important in
the enterprise security tech stack. The secured handshake
ensures the network passage of the data to be transmitted
from source to destination.


Code signing
Code signing is deploying digital signatures to software
applications and hardware-based design firmware. In this
process, cryptographic algorithms are used to develop High-
Assurance seals to help authenticate the software and
hardware. This will help prevent code hacking by making
sure only the code owner and authorized users can sign into
their code development repositories.


There are several improvements that need to be made in the
software engineering and development process, audit and
quality assurance, and control in terms of compliance.


Cryptographic algorithms can support RSA, ECDSA, LMS, and
XMSS hash-based signatures, AES encryption, HMAC-KDF,
CMAC, RSAES-OAEP, and PKCS #11 key wrapping.


Code signing is about having a secure pair of keys to sign a
code package. The certification authority needs to identify
the key used for signing and verify the identity of the
software package provider. Enterprises use code signing to
protect themselves from trojan attacks, operating system
attacks, and others. Users can abort the installation upon
finding a non-verifiable code package before installation.


Now, let us look at the code verification mechanism details:
Figure 12.7: Code verification mechanism
Code signing happens with a private key, as the key cannot
be modified or tampered with. The signature block of code
signature consists of a digest and code signing certificate
and has a function. Code package authentication happens
when the package is downloaded to the computer. The
secure digest is decrypted, and a hash function is used to
derive the developer’s digest. The digest is verified with the
decrypted data and authenticated as safe.


Application code signing
Code signing is the process of applying digital to application
software or to hardware-based device hardware. This method
creates a high assurance seal through cryptographic
algorithms that can be deployed to authenticate whether the
application software created by a trusted provider has not
been tampered with.


Currently, traditional application code signings use RSA or
ECDSA-based key pairs to sign and verify the software. There
is a good scope to start using quantum-resistant application
code signing that uses algorithms that are programmed to
be resistant to quantum computer-based attacks. This can
help secure applications and devices from future attacks.


Application developers use code signing to attach a unique
digital signature to either the software or the hardware
device. This approach ensures the authenticity and integrity
of the operating system, software applications, and hardware
devices, which verify a trusted digital signature to
authenticate and authorize the source of the code and
confirm if the code has not been altered. Now, let us look at
how code signing works:
Figure 12.8: How code signing works
Enterprises are looking for code signing and authentication
software. They are also adopting new hardware security
modules and the latest encryption tools. Quantum
algorithms need to be used to ensure that these techniques
are quantum-safe.


SSH keys
Key issues in SSH key management are access control, key
spread, key rotation, and maintaining communication
secrecy. What will be required is a Key Exchange (KEX)
Algorithm within SSH to help with quantum safety. This will
help protect long-term secrets, sensitive customer data, and
other network protocols like IPsec. In this case, the
responsibility of establishing a secure channel will be based
on Key Exchange. Now, let us look at how SSH keys are used
for user authentication to get server access:
Figure 12.9: SSH keys user authentication for server access
In a cloud-based enterprise, you will come across SSH keys
for different cloud providers’ access. Different tools and
packages are used to provide access and secure
transmission of data from enterprise systems to the cloud.


SSH protocol was invented by Tatu Ylonen in 1995. He
developed it to tackle a sniffing attack on the university
network. SSH keys are based on the RSA 2048-bit encryption
model. The length of the password will be 617 if it is based
on SSH keys. SSH key is a pair of keys that has private and
public keys.


In addition to the cryptographic inventory, it is
recommended that an inventory of application cryptography
threats in the enterprise and a playbook for handling these
threats be built. Now, let us look at the different types of
threats:
Application data over-collection threat: Hidden and
unwarranted collection of personal data needs to be
identified,
tracked,
recorded,
and
aligned
with
applicable data and privacy laws. Compliance checks
need to be done regularly to ensure this data is
updated to avoid this threat.


Application linkage threat: Several customer-facing
apps
hold
a
variety
of
data,
thereby
creating
unwarranted data results by different systems. This
can lead to the linkage of personal data through data
mapping and correlation. Data security systems need
to be in place to avoid linked personal data being
stolen on the cloud or on-premises.


Identification threat: Identity user creation leads to
the association of confidential, sensitive, and personal
data in the form of name, address, gender, and physical
signatures (voice, face). The associated personal data
with
biometrics
and
demographics
need
to
be
protected using monitoring software and auditing the
data access.


Application lifecycle transitions leakage: Leakage
of user personal information from devices in a certain
stage of their application access lifecycle when the
devices are not under owner (user) control. Agents
need to be installed in the devices to monitor user
behavior. User behavior analytics helps prevent data
leakage and predict leakage when it is going to
happen.


Privacy-violating interactions and presentation
leakage: The presentation of user data through
various channels, like voice and video screens, could be
placed in the public domain. This could lead to the
disclosure of user-sensitive information without any
safeguards in place. Sensitive data needs to be
protected.


Localization data leakage: Un-intended leakage of a
user’s location by Global Positioning System (GPS)
coordinates, IP addresses, latency, or cell phone
location. The user’s location can be used for analyzing
and
recommending
the
enterprise
products
and
services available at the location. User location needs
to be subscribed by the services with the user’s
permission.


Behavioral
leakage:
Unauthorized
tracking
and
recording of user’s behavior in a certain time and
place. In the case of default user monitoring,
compliance violations need to be flagged and escalated
for changing the defaults.


Tracking attack: An attacker can trace and record a
person’s movement through time and space (based on
localization
or
behavioral
leakages
and
user
identification). An abnormal tracing or recording of a
user’s location needs to be alerted, and an attack can
be prevented before the user’s privacy is violated.


Profiling attack: It is possible for a fraudster to create
duplicate profiles to analyze information about users
and infer their inteRESTs by mapping their profiles and
data. Sniffing of data through the network and
phishing attacks try to use the profile to imitate the
user or a company. Different software is available to
prevent sniffing and phishing attacks.


Inventory attack: An attacker can fire up selective
query requests to the database and analyze the related
responses to determine inteRESTing patterns of users
in the form of unauthorized detection of health issues,
intellectual property, and industrial espionage. Any
leakage of information that is flagged as an IP issue
needs to be escalated and monitored by the enterprise
legal department.


Identity-theft attack: A hacker can steal user identity
(credentials) and misuse their financial and business
services, damaging a given user’s reputation. User
identity needs to be protected by having user identity
governance and identity management solutions in the
enterprise.


Trust exchange
Trust exchange helps provide authentication mechanisms-
based platforms for all systems in an enterprise. This
platform creates a network of players in the enterprise and
the capability to create sub-networks within the network.


Trust can be generated through digital certificates across
applications by unifying process tasks for the issuance, re-
issuance, installation, configuration, and renewal of public
TLS/SSL, verified mark, code signing, document signing, and
user and S/MIME certificates all in one central place.


Digital certificates can be of different types: Class 1, Class 2,
Class 3, and Class 4. Class 1 is based on the email address of
the requesting user. Class 2 requires more information from
the requested user. Class 3 is issued after user identity is
verified. Class 4 certificates are used by enterprises and
companies. Now, let us look at the unification of digital
certification process tasks:
Figure 12.10: Digital certificates unification of process tasks
The renewal of the digital certificates depends on various
certification steps, as shown in the following figure:
Figure 12.11: Digital certificates unification of process tasks
Code signing is the process of deploying digital signatures to
software applications and hardware-based design firmware.


In this process, cryptographic algorithms are used to develop
high-assurance seals to help authenticate the software and
hardware. This will help prevent code hacking by making
sure only the code owner and authorized users can sign in to
their code development repositories.


Digital certificates are based on the basic certificate chain of
trust model. Certificate chaining builds trust between the
end, intermediate, and root entities. Now, let us look at the
chain of trust with digital certificates:
Figure 12.12: Digital certificates certificate chain of trust
The advanced model of the certificate chain of trust is to
have a root trust store. The root certificate trust store can
issue group certificates and manage private key-based
certificates. Trust models based on certificate chains are of
three types: hierarchical, web of trust, and bridge certificate
authority architecture. Now let us look at the different trust
models:
Figure 12.13: Trust models
Let us consider a hypothetical scenario for the example.


Alice and Bob are friends living in different places. They work
in different companies and want to exchange information.


Alice and Bob's information exchange is based on trust. Trust
exchange, when applied with different mechanisms, as
shown in Figure 12.14, helps secure information exchange
between Alice and Bob:
Figure 12.14: Trust exchange-based information sharing
Now, let us look at the digital certificate account profile
management. The first step is to set up digital certificate
account profile management. Now, let us look at the profile
menus:
Figure 12.15: My Profile
Through the Profile Settings page, we can perform the
following Profile management actions:
User personal information (Name, Role, Contact, Job
Title, etc.)
Account information (username, password, email, and
security question)
API keys
Account language preference
Now, let us look at the profile management features:
Figure 12.16: Profile Management
Digital certificate orchestration management will help set up
the unified management for the following processes:
Ordering of user certificate
Re-issuance of user certificate
Renewal of user certificate
Cancel pending user certificate orders
Cancel pending user certificate reissues
User certificate revocation process
Resending email validation for client certificate email
Resending the create user certificate email
Turning on user certificate renewal notifications
Configuration of the user certificate approval process
Generation of the user certificate
Configuration of Email Security Plus Personal ID
Certificate
Figure 12.17: Digital certificate orchestration management
Digital certificate orchestration management goes through
the process steps as shown in Figure 12.17. Now, let us look
at the SAML certificate orchestration management.


Security assertion markup language (SAML) certificate
orchestration management will help connect the identity
provider (IdP) with CertCentral so that non-CertCentral
account users can use their single sign-on (SSO)
credentials to order a client certificate. A SAML admin
certificate Orchestration Management will help set up the
unified management for the following processes:
SAML certificate workflow for requests with pre-
requirement details
SAML certificate requests service workflow
SAML request for a certificate workflow
Configuration of SAML certificate requests
Turning off SAML certificate requests
RESToration of access to SAML certificate requests
accounts
Request process for a user certificate
Generation of user certificate
Downloading a copy of your user certificate
Submission of a request to revoke a user certificate
Resending the create user certificate email
Allow access to SAML settings
Figure 12.18: SAML admin certificate orchestration management
SAML admin certificate orchestration is based on the steps
shown in Figure 12.18. Now, let us look at the Trust
Certificate Lifecycle Management.


Setting up the Trust Certificate Lifecycle Management is
based on the following points:
Setup of agent: Installation of Trust Certificate
software on Server/Cloud for Certificate Automation
Management.


Setup of business unit: Organisational units will be
defined. This will help in segment definition and
allocation of certificate issuance and management.


Setup of Trust Certificate: A Trust Certificate will
enhance Digital Trust for authentication, validation,
and encryption of digital resources.


Setup
of
Trust
Certificate
Profile:
A
set
of
predefined Certificate Template options on rules for
Format and issuance of a particular type of certificate.


Setup of Certificate Template: These are required
for foundational records, which help as a starting point
for creation of Trust certificate Profiles
Trust Certificate Connector: End-to-end complete
Trust Lifecycle Management for external accounts,
applications, and certificate authorities connected to
user account
DNS integration: Trust-based integrated application
used for validation checks of DNS-based domain
Trust Certificate Enrolment: Certificate requests by
a
user
based
on
Trust
Certificate
Life
Cycle
Management
Trust Certificate Integration: Unified Integration
across all external software, devices, and connectors
that are accessible and continuously validated
Issuing Trust Certificate: Unified management is
needed
for
the
root
or
intermediate
certificate
authority to issue end-entity certificates. This will help
manage certificates from a variety of issuing CAs.


Lifecycle management: Tracking public and private
certificates through enrolment, issuance, expiration,
and renewal, as well as revocation and reissuance.


Seat
and
License
Management:
License
management
across
On-Prem
and
cloud-based
infrastructure
Sensor: Trust Certificate will be installed on enterprise
networks to discover and automate the management of
certificates on network appliances and cloud services.


Sensor connection: A physical network appliance or
cloud service that connects to a sensor for the purpose
of trust certificate lifecycle management. Additionally,
any application (for example, a DNS integration) that
connects to the sensor facilitates the automation of
lifecycle events.


Figure 12.19: Trust Certificate Lifecycle Management
Figure 12.19 shows the different steps in the Trust Certificate
Lifecycle Management workflow.


Real-life examples
Trust exchange is used in social enterprises and community
groups. Trust exchange provides the following features to the
social enterprises:
Tracking of product specifications
Artifact management
Supplier assessments and scoring
Regulation and compliance requirements
Collaboration between enterprise and suppliers
Monitoring and management of systems
Artifact template management
Realtime notifications about products and services
Realtime tracking of orders and delivery
Granular entitlements for supplier users
Organization model and roles management
Message and alert management
Supplier performance management
Another real-life example where trust exchange can play an
important role is the real estate management system. Trust
exchange features which impact the users are listed as
follows:
Real estate application management
Real estate document management
Visibility and tracking of applications
Task management
Real-time collaboration between real estate sellers and
buyers
Digital certificate management
Application alerts management
Event reporting
Bulk SMS and Email campaigns
Buyers and owners of KYC
Secured message delivery
Another real-world example is in the healthcare domain.


Trust exchange plays an important role by providing the
features listed for healthcare centers listed as follows:
Doctors,
patients,
staff,
and
nurses
identity
management
Emergency alerts for patients and staff
Information security and trust management
Health center user authentication and authorization
Search
capability
for
retrieving
documents
and
artifacts
Maintain compliance standards for data security and
storage
Multiple-factor authentication for web, mobile, and
desktop applications
Secured tracking and monitoring of intruders
Intrusion detection and prevention system capabilities
User identity governance and data governance
Securing REST API
We are revisiting the CRM microservices example from
Chapter 7, Go Real Life Applications—CRM. In this section,
we are going to enhance it with authorization and
authentication of the REST API. Now, let us look at the
main.go code:
main.go
package main
import (
"fmt"
"go_beego_REST_api/pkg/db"
handler "go_beego_REST_api/pkg/handlers"
"log"
"net/http"
"github.com/gorilla/mux"
)
func main() {
r := mux.NewRouter()
DB := db.InitializeDB()
r.HandleFunc("/customers",
handler.GetCustomers(DB)).Methods("GET")
r.HandleFunc("/create",
handler.CreateCustomer(DB)).Methods("POST")
r.HandleFunc("/update",
handler.UpdateCustomer(DB)).Methods("PUT")
r.HandleFunc("/delete",
handler.DeleteCustomer(DB)).Methods("DELETE")
fmt.Println("Server at 9090")
server := http.Server{
Addr:    ":9090",
Handler: handler.SecureHandler(r),
}
log.Fatal(server.ListenAndServe())
}
The following figure shows the Beego web app directory
structure:
Figure 12.20: REST API example
The above main.go has the code for initializing the ORM and
registering the model. Beego is started in the main method.


Now, let us look at the db.go code:
db.go
package db
import (
"fmt"
"github.com/jinzhu/gorm"
_
"github.com/jinzhu/gorm/dialects/postgres"
)
func InitializeDB() *gorm.DB {
db, err := gorm.Open("postgres",
"user=newuser password=newuser dbname=crm
sslmode=disable")
if err != nil {
fmt.Println(err)
} else {
fmt.Println("DB connected!")
}
return db
}
handler.go has the code for tableName definition and the
object class specification:
handler.go
type CustomerBody struct {
Name string `json"name"`
}
type Customer struct {
Id      int    `json:"id"`
Name    string `json:"name"`
Mobile  string `json:"mobile"`
Address string `json:"address"`
}
GetCustomers method
func GetCustomers(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var customers []Customer
_ =
db.Table("customer").Select("id,
name,mobile,address").Scan(&customers)
json.NewEncoder(w).Encode(customers)
}
}
CreateCustomer method
func CreateCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var RequestBody CustomerBody
json.NewDecoder(r.Body).Decode(&RequestBody)
_ =
db.Table("customer").Create(&RequestBody)
fmt.Println("Created Customer")
json.NewEncoder(w).Encode(RequestBody)
}
}
UpdateCustomer method
func UpdateCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var PutBody Customer
json.NewDecoder(r.Body).Decode(&PutBody)
_ =
db.Table("customer").Where("id=?",
PutBody.Id).Update("name",
PutBody.Name).Scan(&PutBody)
fmt.Printf("Updated Customer with
id %d\n", PutBody.Id)
json.NewEncoder(w).Encode(PutBody)
}
}
DeleteCustomer method
func DeleteCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var DeleteBody Customer
json.NewDecoder(r.Body).Decode(&DeleteBody)
_ =
db.Table("customer").Delete(&DeleteBody)
fmt.Printf("Deleted Customer with
id %d\n", DeleteBody.Id)
json.NewEncoder(w).Encode(DeleteBody)
}
}
SecureHandler method
func SecureHandler(hthandler http.Handler)
http.HandlerFunc {
return func(rwriter http.ResponseWriter, rq
*http.Request) {
error := godotenv.Load(".env")
if error != nil {
log.Fatalf("Error loading
environment variables file")
}
user, pass, ok := rq.BasicAuth()
if ok {
userid :=
sha256.Sum256([]byte(os.Getenv("USER_ID")))
password :=
sha256.Sum256([]byte(os.Getenv("PASSWORD")))
userIdHash :=
sha256.Sum256([]byte(user))
passwordHash :=
sha256.Sum256([]byte(pass))
validUserName :=
subtle.ConstantTimeCompare(userIdHash[:],
userid[:]) == 1
validPassword :=
subtle.ConstantTimeCompare(passwordHash[:],
password[:]) == 1
if validPassword &&
validUserName {
hthandler.ServeHTTP(rwriter, rq)
return
}
}
http.Error(rwriter, "No/Invalid
Credentials", http.StatusUnauthorized)
}
}
Now, let us look at the database script to create a table in
postgres crm database. You can use the following
commands to create a postgres database CRM and users:
psql -h localhost -d postgres
----
ALTER USER postgres PASSWORD 'postgres';
CREATE USER newuser with PASSWORD 'newuser'
CREATEDB;
select * from users;
\du
\l
\q
-----
createdb crm
psql -h localhost -d postgres
\c crm
CREATE TABLE "customer" (
"id"  SERIAL,
"name" varchar(200) NOT NULL,
"mobile" varchar(100),
"address" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
\dt
\du
alter role newuser superuser;
create user newuser with password 'newuser';
grant all privileges on database crm to newuser;
alter role newuser superuser;
Now, let us look at the database sql for the creation of the
schema:
Database
CREATE TABLE "customer" (
"id" serial,
"name" varchar(200) NOT NULL,
"mobile" varchar(100),
"address" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
create user newuser with password 'newuser';
grant all privileges on database crm to newuser;
Now, let us look at the routes configured in index.go.


The following table shows the routes/paths mentioned in the
index.go:
Action
Controller
method
Http
method
URL route
Description
Create
Add
POST
/create
Create a New
Customer
Read
getAll
GET
/customers
Retrieve list of
customers
Delete
Delete
DELETE
/delete
Delete a
Customer
Update
Update
PUT
/update
Update a
Customer
Table 12.1: Routes and Paths in index.go
You can run the Beego REST API server by using the following
commands:
go mod init go_beego_REST_api
go mod tidy
go build
The output of the REST API server execution is as follows:
Figure 12.21: go_beego_rest_api output
Unit testing the REST API
In this section, we will investigate how postman is used for
unit testing of the REST API. You can test the API unit using
Postman. The Postman collection can be imported using the
JSON provided in the code.


Create Customer
Now let us look at the create customer REST API.


Figure 12.22: Create Customer API
Postman can be used to test the REST API. In Figure 12.22,
the customer is created with a post-method-based REST API
call.


Get Customers
Now, let us look at the Get Customers REST API and the
output when the API is invoked:
Figure 12.23: Get Customers API
Now, let us look at creating another customer and the output
when the API is invoked:
Figure 12.24: Create Customer API Result
Now, let us look at the list of customers created and the
output when the API is invoked:
Figure 12.25: Get Customers after the creation of a customer
Figure 12.25 shows all customers retrieving the results from
a GET method REST API call.


Update Customer
Now, let us look at updating the existing customer and the
output when the API is invoked.


Figure 12.26: Update Customer API
Now, let us look at updating another customer after setting
the authorization info and the output when the API is
invoked.


Figure 12.27: Update Customer Result
Figure 12.27 shows how postman is used for updating the
customer. PUT method-based REST API is used for updating
the customer in the CRM portal. Now, let us look at the list of
the customers and the output when the API is invoked:
Figure 12.28: Get Customers after Update Customer
Delete Customer
Now, let us look at deleting a customer and the output when
the API is invoked:
Figure 12.29: Delete Customer API
Now, let us look at deleting the customer after setting the
authorization info and the output when the API is invoked.


Figure 12.30: Delete Customer Result
DELETE Method-based REST API is called using Postman for
deleting a customer, as shown in Figure 12.29.


Get Customers after Delete
Now let us look at the list of customers after deleting a
customer and the output when the API is invoked:
Figure 12.31: Get Customers after Delete Customer
Figure 12.31 shows the list of customers after update and
delete operations.


Conclusion
In this chapter, we have covered topics related to basic and
advanced security services. We looked at security principles
for protecting microservices.


We looked at basic and advanced security services to secure
the microservices. Different security mechanisms and
principles were discussed relevant to microservices security.


You learned about methods to secure web applications built
on microservices-based architecture. We discussed an
example to secure the REST API-based microservices.


In the next chapter, readers will understand different design
patterns, such as creational, structural, and behavioral
patterns. Code samples will be provided for the Gang of Four
Design patterns. The readers will also understand object-
oriented design principles using Go.

