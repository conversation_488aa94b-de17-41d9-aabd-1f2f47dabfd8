# 10 CHAPTER 10 Containerization and Docker

    Containers
    Docker
    Go Services
    Dockerized Go Services

Enterprises are adopting containerization for easy deployment of microservices on the cloud and on-premises. 

Tech Stack, which is containerized in the docker:

Containers provide capabilities to scale, perform, agile, control, isolate, and be portable across different operating systems. Let us take an example where an enterprise wants to build a web application to enable selling online. This e-commerce application can be built on a Go language framework like Beego. It can expose REST API and React web applications can access the REST API. These APIs are
deployed on the Beego web framework. We can use Linux Ubuntu as the operating system. The image can have Go language binaries and compiler setup. Beego framework can be installed on the image.

Once the web application with web pages on React and
integration is done with REST API through JavaScript, the
web application can be deployed on the image and tested by
the QA engineers. The image can be uploaded to the docker
hub, which is the registry. On the cloud, you can access this
image for deployment and execute the application. There is
a huge cut down in cost, time, and effort for testing,
deployment, and project execution. Now, let us look at the
Go Lang Tech Stack dockerized in a Linux container:
Figure 10.3: Go Lang Tech Stack—Linux
We can have performance testing on the product
environment with higher-end RAM like 32 GB and have 4 VMs
running the web application. 4 VMs can each have 8 GB of
memory. On any cloud, this can be achieved without actual
web app deployment. Docker container helps manage
memory allocation very easily with a Docker cluster
environment. Docker container virtualizes the operating
system and provides capabilities to manage CPU, memory,
disk space, network, and IO.


Now, let us first create an image in a container. We will be
using Docker as the container. You need to register on
hub.docker.com and download the Docker software. You have
to first create a Dockerfile. Now, let us look at the Dockerfile:
Dockerfile
FROM busybox
CMD echo "Your first image on container is created"
You need to run these commands to build the image.


docker build -t bhagvanarch/docker-container-image
.


docker run bhagvanarch/docker-container-image
The output of the above commands, when executed, is as
follows:
Figure 10.4: Docker container image execution output
As shown in the example, input for the docker process is the
Docker file. The Docker file has the steps and tasks defined
for creating an image.


Containers are used not only for microservices-based
applications but also for domain-driven design pattern-based
apps. Container orchestration can be done by having
multistage builds and multiple processes initialized by the
container. It goes without saying that a single monolithic
application can be deployed in a container.


Docker
Docker is a popular container that is used for enterprise app
deployment on the cloud and on-premises. Most of the time,
developers and deployment engineers argue about where it
was working vs. where it is not currently working. Developers
try their best in these arguments to show their environment
is working fine. Deployment engineers strongly protest,
saying that it is not working on the prod environment,
though the operating system and database are the same.


This is where Docker comes to the rescue. All the steps and
tasks performed by the developer are captured in the
Dockerfile. This Dockerfile is used as the input for the Docker
executable for the deployment engineers to deploy in the
production environment. Complex environments, which have
many steps and tasks to perform, can be simplified by
having a Dockerfile. Dockerfile typically has environmental
variables, different Go Lang compiler versions, build process
tasks, mounting of directories, and copying tasks.


Docker was created by a company called Dotcloud. Dotcloud
Inc. became Docker Inc. Docker was written in the Go
language. Docker became popular after its creation in 2013
as a DevOps tool. Docker was implemented as the
deployment tool for deploying enterprise apps on the cloud.


Docker is installed on the developer’s desktop as a host,
daemon, client, and hub repository. Docker client and server
communicate through a client-server architecture.


Now, let us first create an image in a docker container where
we can write Go Lang programs. We will be using the
golang:1.8 version and the Docker as the container. You can
first create a Dockerfile using the following code:
Dockerfile
FROM golang:1.8
WORKDIR .


COPY *.go .


RUN go build -o first_go .


CMD ./first_go
You need to run these commands to build the image locally:
docker build -t first_go_image -f Dockerfile .


docker run -it first_go_image bash
The output of the above commands, when executed, is as
follows:
first_go_image
Figure 10.5: First Docker image execution output
Now, let us look at the Docker Registry.


Docker Registry
Docker Registry is used to store the Docker images. The
images can be stored in repos. These repos can be public or
private. Developers can download the images for
development. QA engineers can do the same for testing.


Docker Hub is a Docker Registry that is public.


Each cloud provider, such as AWS, Azure, Google Cloud, and
others, provides a container registry. Different companies
can have private container registries.


Now, let us create the image on the docker hub (registry)
where we can write Go Lang programs. We will be using the
golang:1.8 version and the Docker as the container. You can
first create a Dockerfile. Now, let us look at the Dockerfile:
Dockerfile
FROM golang:1.8
WORKDIR .


COPY *.go .


RUN go build -o first_go .


CMD ./first_go
You need to run the following commands to build the image
on the docker hub:
docker build -t bhagvanarch/first_go_image -f
Dockerfile .


docker run -it bhagvanarch/first_go_image bash
The output of the above commands, when executed, is as
follows:
Bhagvanarch/first_go_image
Figure 10.6: First Docker Image execution output
Docker Compose
Docker Compose is used to build images of multiple
containers. Multi-stage builds and multiple processes
involving different containers can help in creating and
packaging multiple container applications. In the example
above, where we were creating an enterprise web
application based on Beego Web framework and React, we
can use Docker Compose for multiple containers having
database MySQL, Beego-based REST API, and React web
application. These containers are configured in the YAML file.


Docker-compose can run in the background if you use the -d
flag. Now, let us look at the React.js web app built using Gin
REST API as the backend:
Figure 10.7: React.js web app—Gin Rest API
Now, let us create the image using docker-compose, where
we can write the Go Lang programs. We will be using the
golang:1.8 version and the Docker as the container. You can
first create a Dockerfile. Now, let us look at the Dockerfile:
Dockerfile
FROM golang:1.8
WORKDIR .


COPY *.go .


RUN go build -o first_go .


CMD ./first_go
Now, let us create the docker-compose.yml:
docker-compose.yml
version: '2'
services:
first-docker-compose-go:
build: .


environment:
- TEST_ENV=test
You need to run these commands to build the image on the
Docker Hub:
docker-compose up
docker-compose run first-docker-compose-go bash
The output of the above commands, when executed, is as
follows:
first_docker_compose_go_image
Figure 10.8: First Docker Compose image execution output
Docker networks
You can create a container and build images in any operating
system using docker or docker-compose. When you can
execute an image, you can see there is an IP address for
each image running on your computer. You can have a
docker network setup and have docker images assigned to
the network images. Docker network types are default, user-
defined, and overlay docker network.


You can specify the network parameter while creating a
container. The default network created will be a container on
a bridge network. Containers can talk to each other using IP
addresses. You can have multiple containers running on one
host. The bridge network has one host. If you want to have
multiple Docker hosts, you can use the network parameter
and assign a host value. The host network stack will have
multiple host details when you create them. Containers that
are part of the host network can talk to the host network
interfaces. You can have no network assigned if you pass the
value none to the network parameter. The container will not
have any IP address assigned.


Go Services
You can build microservices with the Go language. Moving
away from the monolithic approach of building apps,
microservices architecture is known for building the
application ground up with micro-services. Microservices can
talk to each other in different protocols like HTTP, AMQP,
HTTPS, and web sockets. Every microservice can be
deployed and executed as a separate process. Every
microservice will have its own data model, business logic,
rules, and data storage mechanism. The backend database
can be NoSQL or a relational database. Microservices
architecture is getting popular because of its benefits, which
are related to manageability, scalability, performance, and
time-to-market delivery.


Let us now discuss building REST API (Microservice) with MQ.


We built this Go Lang microservice in Chapter 4, Building
REST API. In this context of building a Go Service Docker, let
us relook at how to develop a REST API that can interact with
message queues. We are going to use Gin and Rabbit MQ.


Let us look at the code for developing the REST API using Gin
and RabbitMQ.


main.go
package main
import (
"fmt"
"github.com/gin-gonic/gin"
"github.com/rs/zerolog/log"
"rest_api_mq/producer/config"
"rest_api_mq/producer/utils"
)
func init() {
mode := utils.GetEnvVar("GIN_MODE")
gin.SetMode(mode)
}
func main() {
appGin := config.CreateApp()
addrGin := utils.GetEnvVar("GIN_ADDR")
portGin := utils.GetEnvVar("GIN_PORT")
log.Info().Msgf("App is up at
http//:%s:%s", addrGin, portGin)
if error := appGin.Run(fmt.Sprintf("%s:%s",
addrGn, portGin)); error != nil {
log.Fatal().Err(error).Msg("Http
Server setup failed")
}
}
Now, let us look at the createApp to see how the routes are
set up through routers:
Creating_app.go
package config
import (
"github.com/gin-gonic/gin"
"github.com/rs/zerolog/log"
"rest_api_mq/producer/middlewares"
"rest_api_mq/producer/routers"
)
func CreateApp() *gin.Engine {
log.Info().Msg("service starting")
app := gin.New()
app.Use(gin.Recovery())
app.SetTrustedProxies(nil)
log.Info().Msg(" cors, request id, request
logging middleware added")
app.Use(middlewares.CORSMiddleware(),
middlewares.RequestID(),
middlewares.RequestLogger())
log.Info().Msg("routers setup")
routers.SetupRouters(app)
return app
}
The Routers package has the routes mapped to methods that
talk to the message queue. Now, let us look at the setup.go
code:
Get   /ping     controllers.ping
Post /publish/example controllers.Example
setup.go
package routers
import (
"github.com/gin-gonic/gin"
"rest_api_mq/producer/controllers"
)
func CreateRouters(engine *gin.Engine) {
version1 := engine.Group("/v1")
{
version1.GET("/ping",
controllers.Ping)
version1.POST("/publish/example",
controllers.Example)
}
}
The controllers package has the message publisher
configured to publish messages to a message queue.


controllers.go
package controllers
import (
"net/http"
"github.com/gin-gonic/gin"
"github.com/rs/zerolog/log"
"rest_api_mq/producer/environment"
"rest_api_mq/producer/models"
"rest_api_mq/producer/utils"
)
func Example(context *gin.Context) {
var msg models.Message
request_id := context.GetString("x-request-
id")
if binderr := context.ShouldBindJSON(&msg);
binderr != nil {
log.Error().Err(binderr).Str("request_id",
request_id).


Msg("Error occurred while
binding request data")
context.JSON(http.StatusUnprocessableEntity, gin.H{
"message": binderr.Error(),
})
return
}
connectionString :=
utils.GetEnvVar("RMQ_URL")
producer := utils.MessagePublisher{
environment.EXAMPLE_QUEUE,
connectionString,
}
producer.PublishMessage("text/plain",
[]byte(msg.Message))
context.JSON(http.StatusOK, gin.H{
"response": "Message received from
Rest API",
})
}
MessagePublisher is a struct defined in the utils package.


It has a method to publish messages to a queue defined in
the environment. Now, let us look at the Publisher.go code:
Publisher.go
package utils
import (
"github.com/rs/zerolog/log"
"github.com/streadway/amqp"
)
type MessagePublisher struct {
Queue            string
ConnectionString string
}
func (x MessagePublisher) OnError(err error, msg
string) {
if err != nil {
log.Err(err).Msgf("Publishing
message error '%s' queue. Error message: %s",
publisher.Queue, msg)
}
}
func (publisher MessagePublisher)
PublishMessage(contentType string, body []byte) {
conn, error :=
amqp.Dial(publisher.ConnectionString)
publisher.OnError(error, "RabbitMQ not
connected")
defer conn.Close()
channel, err := conn.Channel()
publisher.OnError(err, "Channel not
opened")
defer channel.Close()
q, error := channel.QueueDeclare(
publisher.Queue,
false,
false,
false,
false,
nil,
)
publisher.OnError(error, "Queue Not
declared")
error = channel.Publish(
"",
q.Name,
false,
false,
amqp.Publishing{
ContentType: contentType,
Body:        body,
})
publisher.OnError(error, "message not
published")
}
Now, let us look at the consumer package and the
implementation of main.go in code:
main.go
package main
import (
"rest_api_mq/consumer/environment"
"rest_api_mq/consumer/handlers"
"rest_api_mq/consumer/utils"
)
func main() {
connectionString :=
utils.GetEnvVar("RMQ_URL")
messageQueue := utils.MessageConsumer{
enviornment.EXAMPLE_QUEUE,
connectionString,
handlers.HandleMessaage,
}
forever := make(chan bool)
go messageQueue.Consume()
<-forever
}
MessageConsumer is created from the utils package. Now,
let us look at the Message_consumer.go code:
Message_consumer.go
package utils
import (
"github.com/rs/zerolog/log"
"github.com/streadway/amqp"
)
type MessageConsumer struct {
Queue            string
ConnectionString string
MsgHandler       func(queue string, msg
amqp.Delivery, err error)
}
func (consumer MessageConsumer) OnError(errors
error, msg string) {
if errors != nil {
consumer.MsgHandler(consumer.Queue,
amqp.Delivery{}, errors)
}
}
func (consumer MessageConsumer) Consume() {
conn, err :=
amqp.Dial(consumer.ConnectionString)
consumer.OnError(err, "Failed to connect to
RabbitMQ")
defer conn.Close()
channel, err := conn.Channel()
consumer.OnError(err, "Failed to open a
channel")
defer channel.Close()
q, err := channel.QueueDeclare(
consumer.Queue,
false,
false,
false,
false,
nil,
)
consumer.OnError(err, "Failed to declare a
queue")
msgs, err := channel.Consume(
q.Name,
"",
true,
false,
false,
false,
nil,
)
consumer.OnError(err, "Failed to register a
consumer")
forever := make(chan bool)
go func() {
for delivery := range msgs {
consumer.MsgHandler(consumer.Queue, delivery, nil)
}
}()
log.Info().Msgf("Started listening-
messages from '%s' queue", consumer.Queue)
<-forever
}
The environment package has the constants defined for the
environment file, directory, and message queue. Now, let us
look at the constants.go code:
constants.go
package environment
const ENV_FILE = ".env"
const ENV_FILE_DIRECTORY = "."
const EXAMPLE_QUEUE = "message_queue"
.env will Rabbit message queue configuration URL
and Log level.


.env
LOG_LEVEL = debug
RMQ_URL = amqp://guest:guest@localhost:5000/
Each microservice shown above is an independent
deployable unit that can be tested and loosely coupled.


Microservice can consist of small services built and used in
the framework for different features. A small team can build
microservices architecture-based applications very easily
and quickly. Agile process and microservices go together well
in making the development of features meet the time to
market. Polyglot microservices architecture style embraces
tech stacks like Java, Python, Lang, PHP, and .NET.


Microservice architecture provides benefits such as good
readability of the code, components decentralization,
continuous integration, test automation, continuous
deployment, and domain-driven design.


Dockerized Go Services
Now, let us look at how Docker can help the deployment of
the Go Lang microservices more effectively. In the following
example, we will look at how to develop REST API
(Dockerized microservice), which can interact with message
queues. We are going to use Gin and RabbitMQ.


The Environment package has the constants defined for the
Environment file, directory, and message queue. Now, let
us look at the constants.go code:
constants.go
package environment
const ENV_FILE = ".env"
const ENV_FILE_DIRECTORY = "."
const EXAMPLE_QUEUE = "message_queue"
.env will Rabbit message queue configuration URL
and Log level.


.env
LOG_LEVEL = debug
RMQ_URL = amqp://guest:guest@localhost:5000/
docker-compose.yaml
networks:
rabbitmq-example:
driver: bridge
services:
rabbitmq:
image: 'rabbitmq:3-management'
networks:
- rabbitmq-example
volumes:
- ./rabbit-
mq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
ports:
- "8080:15672"
healthcheck:
test: [ "CMD", "rabbitmqctl", "status"]
interval: 5s
timeout: 15s
retries: 5
producer:
build: ./producer
ports:
- "5050:5050"
networks:
- rabbitmq-example
depends_on:
- rabbitmq
environment:
GIN_MODE: "release"
GIN_HTTPS: "false"
GIN_ADDR: "0.0.0.0"
GIN_PORT: "5050"
LOG_LEVEL: "debug"
RMQ_URL: "amqp://guest:guest@rabbitmq:5673/"
consumer:
build: ./consumer
networks:
- rabbitmq-example
depends_on:
- rabbitmq
restart: on-failure
environment:
LOG_LEVEL: "debug"
RMQ_URL: "amqp://guest:guest@rabbitmq:5673/"
You can now compile and run the rabbitmq, producer, and
consumer services in the docker. The command to run the
docker is as follows:
docker-compose up
The output will be as follows:
Figure 10.9: MQ Docker Compose image execution output
In the above example, we have used docker-compose to
build a Go Lang microservice based on MQ.


Conclusion
In this chapter, we have covered topics related to containers,
Docker, Docker Registry, Docker Compose, Docker networks,
Go Lang microservices, and Dockerized Go Lang
microservices.


Containers like Docker and Kubernetes are used to build and
deploy software packages. Docker has components like
registry and networks to have multiple client-host models.


Docker Compose helps build multiple stages and services.


The Go Lang tech stack can be used to build microservices.


Dockerized Go Services can be built using docker-compose
and multiple Go Lang Services.


In the next chapter, the reader will understand the principles
of microservices architecture compared to monolithic
architecture. The reader will then be able to create an
architecture using Microservices and deploy these services
on containers.

