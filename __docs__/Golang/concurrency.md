# Concurrency in Go: Patterns and Flow Control

Go's concurrency model is one of its most powerful features, built around the philosophy of "Don't communicate by sharing memory; share memory by communicating." Let me explain the key concepts, patterns, and flow control mechanisms.

## Core Concurrency Primitives

### 1. Goroutines
Goroutines are lightweight threads managed by the Go runtime. They're much cheaper than OS threads:

```go
// Starting a goroutine
go func() {
    fmt.Println("Running in a goroutine")
}()

// Or with a named function
go myFunction()
```

### 2. Channels
Channels are the primary way goroutines communicate:

```go
// Unbuffered channel
ch := make(chan int)

// Buffered channel
buffered := make(chan int, 10)

// Send and receive
ch <- 42        // Send
value := <-ch   // Receive
```

### 3. Select Statement
The `select` statement allows you to wait on multiple channel operations:

```go
select {
case msg1 := <-ch1:
    fmt.Println("Received from ch1:", msg1)
case msg2 := <-ch2:
    fmt.Println("Received from ch2:", msg2)
case <-time.After(1 * time.Second):
    fmt.Println("Timeout!")
default:
    fmt.Println("No channels ready")
}
```

## Common Concurrency Patterns

### 1. For-Select-Done Pattern
This pattern uses an infinite loop with select to handle events and graceful shutdown:

```go
func worker(ctx context.Context) {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            // Do periodic work
            doWork()
        case <-ctx.Done():
            fmt.Println("Worker stopping")
            return
        }
    }
}
```

### 2. Pipeline Pattern
Pipelines process data through multiple stages connected by channels:

```go
// Generator stage
func generate(nums ...int) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for _, n := range nums {
            out <- n
        }
    }()
    return out
}

// Processing stage
func square(in <-chan int) <-chan int {
    out := make(chan int)
    go func() {
        defer close(out)
        for n := range in {
            out <- n * n
        }
    }()
    return out
}

// Usage
numbers := generate(1, 2, 3, 4)
squared := square(numbers)
for result := range squared {
    fmt.Println(result)
}
```

### 3. Fan-Out/Fan-In Pattern
**Fan-out**: Distribute work across multiple goroutines
**Fan-in**: Combine results from multiple goroutines

```go
// Fan-out: multiple workers reading from same channel
func fanOut(in <-chan int, workers int) []<-chan int {
    channels := make([]<-chan int, workers)
    for i := 0; i < workers; i++ {
        out := make(chan int)
        channels[i] = out
        go func() {
            defer close(out)
            for n := range in {
                out <- process(n)
            }
        }()
    }
    return channels
}

// Fan-in: merge multiple channels into one
func fanIn(channels ...<-chan int) <-chan int {
    var wg sync.WaitGroup
    out := make(chan int)
    
    for _, ch := range channels {
        wg.Add(1)
        go func(c <-chan int) {
            defer wg.Done()
            for n := range c {
                out <- n
            }
        }(ch)
    }
    
    go func() {
        wg.Wait()
        close(out)
    }()
    
    return out
}
```

### 4. Worker Pool Pattern
Limit the number of concurrent workers processing tasks:

```go
func workerPool(jobs <-chan Job, results chan<- Result, numWorkers int) {
    var wg sync.WaitGroup
    
    // Start workers
    for i := 0; i < numWorkers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for job := range jobs {
                result := processJob(job)
                results <- result
            }
        }()
    }
    
    // Close results when all workers are done
    go func() {
        wg.Wait()
        close(results)
    }()
}
```

### 5. Errgroup Pattern
Handle errors from multiple goroutines using `golang.org/x/sync/errgroup`:

```go
import "golang.org/x/sync/errgroup"

func processWithErrgroup() error {
    g := new(errgroup.Group)
    
    // Start multiple tasks
    g.Go(func() error {
        return task1()
    })
    
    g.Go(func() error {
        return task2()
    })
    
    g.Go(func() error {
        return task3()
    })
    
    // Wait for all to complete, return first error
    return g.Wait()
}
```

## Flow Control Mechanisms

### 1. Context for Cancellation
Use `context.Context` for cancellation and timeouts:

```go
func doWorkWithTimeout(ctx context.Context) error {
    // Create a timeout context
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    select {
    case result := <-doLongRunningWork():
        return processResult(result)
    case <-ctx.Done():
        return ctx.Err() // Returns timeout or cancellation error
    }
}
```

### 2. Sync Package Primitives

**WaitGroup**: Wait for multiple goroutines to complete
```go
var wg sync.WaitGroup

for i := 0; i < 10; i++ {
    wg.Add(1)
    go func(id int) {
        defer wg.Done()
        doWork(id)
    }(i)
}

wg.Wait() // Wait for all goroutines to finish
```

**Mutex**: Protect shared resources
```go
var (
    mu      sync.Mutex
    counter int
)

func increment() {
    mu.Lock()
    defer mu.Unlock()
    counter++
}
```

**Once**: Ensure something happens only once
```go
var once sync.Once

func initialize() {
    once.Do(func() {
        // This will only run once, even if called multiple times
        setupResources()
    })
}
```

### 3. Channel-Based Flow Control

**Buffered channels** for rate limiting:
```go
// Limit to 10 concurrent operations
semaphore := make(chan struct{}, 10)

func limitedOperation() {
    semaphore <- struct{}{} // Acquire
    defer func() { <-semaphore }() // Release
    
    // Do work
}
```

**Closing channels** for broadcast signaling:
```go
done := make(chan struct{})

// Signal all goroutines to stop
close(done)

// In goroutines:
select {
case <-done:
    return // Stop working
default:
    // Continue working
}
```

## Best Practices

1. **Always handle goroutine lifecycle**: Ensure goroutines can exit cleanly
2. **Use contexts for cancellation**: Pass `context.Context` to long-running operations
3. **Avoid goroutine leaks**: Make sure all goroutines have a way to terminate
4. **Channel ownership**: The sender should close the channel, not the receiver
5. **Use buffered channels carefully**: They can hide synchronization issues
6. **Prefer channels over shared memory**: Follow Go's concurrency philosophy

## Advanced Patterns

### Rate Limiting with Ticker
```go
func rateLimitedWorker(ctx context.Context, rate time.Duration) {
    ticker := time.NewTicker(rate)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            doWork()
        case <-ctx.Done():
            return
        }
    }
}
```

### Circuit Breaker Pattern
```go
type CircuitBreaker struct {
    failures    int
    maxFailures int
    timeout     time.Duration
    lastFailure time.Time
    mu          sync.Mutex
}

func (cb *CircuitBreaker) Call(fn func() error) error {
    cb.mu.Lock()
    defer cb.mu.Unlock()
    
    if cb.failures >= cb.maxFailures {
        if time.Since(cb.lastFailure) < cb.timeout {
            return errors.New("circuit breaker open")
        }
        cb.failures = 0 // Reset after timeout
    }
    
    err := fn()
    if err != nil {
        cb.failures++
        cb.lastFailure = time.Now()
    }
    
    return err
}
```

Go's concurrency model provides powerful tools for building concurrent applications. The key is understanding when to use each pattern and how to compose them effectively for your specific use case.
