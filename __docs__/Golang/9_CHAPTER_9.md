# 9 CHAPTER 9

Go Dependency Injection
and SOLID
Introduction
In this chapter, the reader will understand dependency
injection using the Go language. The reader will also
understand SOLID principles, which include dependency
inversion. The readers will learn about different types of
dependency injection, such as constructor, property, and
method. Now, let us look at the Solid principles:
Figure 9.1: SOLID Principles
Structure
The chapter covers the following topics:
SOLID principles
Dependency injection
Dependency injection types
Objectives
In this chapter, we are going to look at design principles like
SOLID, dependency inversion, dependency injection, and
dependency injection types. These principles focus on
dependency injection, module dependencies, component
dependencies, and decoupling of the components from the
component using wiring.


SOLID principles
SOLID principles were created by <PERSON>. They are
design tenets for creating and developing software. These
tenets help in making the software extensible, scalable, and
enhance-able.


The SOLID principles are listed as follows:
Single Responsibility Principle (SRP)
Open/Closed Principle (OCP)
Liskov Substitution Principle (LSP)
Interface Segregation Principle (ISP)
Dependency Inversion Principle (DIP)
Now, let us look at the different principles which are part of
SOLID principles:
Figure 9.2: SOLID principles
Let us start by looking at the SRP first.


Single responsibility principle
A class should have one, and only one, reason to change.


–<PERSON> Martin
A class or module needs to have a single responsibility. This
helps in making the software modular. New enhancements
can be added to the software easily. Defects can be
analyzed, and root causes can be detected easily.


In life, we pay for online services and for ordering food and
groceries. Every time we pay online using Google Pay or any
other payment service, we send the order information and
account information to process the order payments. Based
on the SRP, a payment service needs to be responsible for
processing the payments and sharing a response that the
payment has been processed with the transaction
information. The invoice service will process the invoices and
share the invoice link with you. The order management
service will process the orders after communicating the
success of payment to the vendor.


As the payment service matures, new enhancements are
required to process the payment asynchronously or when the
connectivity is available. Based on the success, the order is
processed. As SRP states, this feature can be added to the
payment service very easily. Based on the success of the
payment, invoice, and order services receive the payment
success event to process the workflow.


Customer service maintains the customer information. We
can have customer loyalty service and customer address
service to handle loyalty programs and addresses of the
customer. Typically, loyalty programs will consist of different
levels like Platinum, Gold, Silver, and Bronze. These levels
are defined at different ranges of points. Loyalty points are
gathered by the customer during ordering groceries and
other items. Offers are targeted at different loyalty
membership levels to attract customers to leap from one
membership level to another.


Customer address service manages the address of the
customer. This service has contact information like mobile,
email, and landline contacts. This helps in sending marketing
offers and targeting campaigns to the customer.


First, let us look at the code for SRP. We will start with a
LandVehicle struct, which implements the interface
Vehicle. The code is as follows:
srp_principle.go
package main
import "fmt"
type Vehicle interface {
drive()
}
type LandVehicle struct {
Vehicle
name string
}
func (dr *LandVehicle) init() {
dr.checkBattery()
dr.checkEngine()
}
func (dr *LandVehicle) drive() {
fmt.Printf(">>> driving Car[%s] ... \n",
dr.name)
dr.init()
dr.start()
dr.monitorHealth()
}
func (dr *LandVehicle) checkBattery() {
fmt.Println(" checking battery's status ...


")
}
func (dr *LandVehicle) checkEngine() {
fmt.Println(" checking engine's status ...


")
}
func (dr *LandVehicle) start() {
fmt.Println("starting now ... ")
}
func (dr *LandVehicle) monitorHealth() {
fmt.Println("[ everything is ok ... ")
}
func main() {
dr := &LandVehicle{name: "Car"}
dr.drive()
}
You can now compile and run the srp_example.go, using the
following command:
go run srp_example.go
The output will be as follows:
Figure 9.3: SRP example output
In this example, the LandVehicle has a single responsibility
of handling the driving part.


Open/Closed principle
A software artifact should be open for extension but closed
for modifications.


Software is built in such a way that it is open for extending
functionality and is closed for modifying the existing
functionality. Adding new behaviors to the software can be
done easily, and new features can be added to the software.


This principle helps in making the software agile and
enhanceable with much effort. Using object-oriented
abstraction and interfaces, it is easy to follow these
principles while designing and developing the software.


In the above example of payment service, there are new
payment methods coming up in the market, such as EMI, Buy
Now, Pay Later, and others. Old methods continue to exist,
like credit cards, debit cards, net banking, bill pay, wallets,
and others. Payment service needs to have the capability to
add a new payment method. This can be achieved by having
a Payment Method type as an interface and pass the
interface-typed instance for payment processing function in
Payment Service.


Similarly, the Customer Loyalty service needs to have the
capability to transfer or accept loyalty points from partner
programs. This service needs to have a Loyalty Point Typed
interface as the parameter for addPoints function.


Recap from Chapter 1, we had an example for interface
types:
In Go language, interface is an abstract type that consists of
method signatures. You cannot have instances of the
interface. You can create an interface with type (keyword)
interface name interface (keyword). More than one interface
can be implemented by a data type.


Vehicle is an interface, and car is a struct. When car is
instantiated, it returns a vehicle.


Modifying the example with another struct, we will
demonstrate the OCP. Now, let us look at the code for
ocp_principle.go:
ocp_principle.go
package main
import "fmt"
type vehicle interface {
getSpeed() float64
getDistanceTravelled() float64
}
type Car struct {
brand              string
manufacturing_year int
}
func (Car car) getSpeed() float64 {
return 120
}
func (Car car) getDistanceTravelled() float64 {
return 4000
}
type Truck struct {
brand              string
manufacturing_year int
size               string
}
func (Truck truck) getSpeed() float64 {
return 160
}
func (Truck truck) getDistanceTravelled() float64 {
return 40000
}
func main() {
var veh vehicle
veh = car{"Toyota Tercel", 1997}
fmt.Println("Speed of the Vehicle :",
veh.getSpeed())
fmt.Println("Distance Travelled by the
Vehicle:", veh.getDistanceTravelled())
}
You can now compile and run the instance_type.go. The
command is as follows:
go run instance_type.go
The output will be as follows:
Figure 9.4: Interface type example output
The output shows how the interface abstracts out the
method implementation. Vehicle is an interface, and car is a
struct. When car is instantiated, it returns a vehicle.


Liskov Substitution Principle
As Liskov states, what is wanted here is something like the
following substitution property: If for each object o1 of type
S, there is an object o2 of type T such that for all programs P
defined in terms of T, the behavior of P is unchanged when
o1 is substituted for o2 then S is a subtype of T.


In the software, we have structs, which are interfaces or
classes that can be substituted for other equivalent types. In
the payment service example, the payment method type
interface will help in substituting different struct that
implement this interface.


Modifying the previous example with an enhanced interface
of the vehicle, we will demonstrate the Liskov Substitution
Principle. Now, let us look at the code of the file
lsp_principle.go:
lsp_principle.go
package main
import "fmt"
type vehicle interface {
getSpeed() float64
getDistanceTravelled() float64
start()
}
type car struct {
brand              string
manufacturing_year int
}
func (Car car) getSpeed() float64 {
return 120
}
func (Car car) getDistanceTravelled() float64 {
return 4000
}
func (Car car) start() {
fmt.Printf(">>> starting the [%s] car ...


\n", Car.brand)
}
type truck struct {
brand              string
manufacturing_year int
}
func (Truck truck) getSpeed() float64 {
return 180
}
func (Truck truck) getDistanceTravelled() float64 {
return 40000
}
func (Truck truck) start() {
fmt.Printf(">>> starting the [%s] Truck ...


\n", Truck.brand)
}
func getVehicles() []vehicle {
return []vehicle{&car{}, &truck{}}
}
func main() {
var veh vehicle
veh = car{"Toyota Tercel", 1997}
fmt.Println("Car's Speed of the Vehicle :",
veh.getSpeed())
fmt.Println("Car's Distance Travelled by
the Vehicle:", veh.getDistanceTravelled())
veh = truck{"Land Rover", 2001}
fmt.Println("Truck's Speed of the Vehicle
:", veh.getSpeed())
fmt.Println("Truck's Distance Travelled by
the Vehicle:", veh.getDistanceTravelled())
for _, vehicle := range getVehicles() {
vehicle.start()
}
}
You can now compile and run the lsp_principle.go.


The command is as follows:
go run lsp_principle.go
The output will be as follows:
Figure 9.5: LSP principle example output
The output shows how the interface abstracts out the
method implementation of starting a vehicle, whether it is a
truck or a car. In this case, vehicle is an interface, and car
and truck are structs implementing the interface. You can
have get vehicles, that can handle both car and truck.


Interface Segregation principle
Software clients need not rely on interfaces. Typically,
interfaces are designed with minimal functionality, which
helps reduce the dependencies between the modules.


In the previous example, we add one more interface to group
land vehicles. Let us see this in the following code:
isp_principle.go
type vehicle interface {
getSpeed() float64
getDistanceTravelled() float64
start()
}
type group interface {
addToTheGroup()
}
type car struct {
brand              string
manufacturing_year int
}
type truck struct {
brand              string
manufacturing_year int
}
func main() {
var veh vehicle
veh = car{"Toyota Tercel", 1997}
fmt.Println("Car's Speed of the Vehicle :",
veh.getSpeed())
fmt.Println("Car's Distance Travelled by
the Vehicle:", veh.getDistanceTravelled())
var truck1 truck
truck1 = truck{"Land Rover", 2001}
fmt.Println("Truck's Speed of the Vehicle
:", truck1.getSpeed())
fmt.Println("Truck's Distance Travelled by
the Vehicle:", truck1.getDistanceTravelled())
var truck2 truck
truck2 = truck{"Hyundai Trunk", 2011}
fmt.Println("Truck's Speed of the Vehicle
:", truck2.getSpeed())
fmt.Println("Truck's Distance Travelled by
the Vehicle:", truck2.getDistanceTravelled())
truck2.addToTheGroup(truck1)
}
You can now compile and run the isp_principle.go. The
command is as follows:
go run isp_principle.go
The output will be as follows:
Figure 9.6: ISP principle example output
The output shows how the interface abstracts out the
method implementation of adding a vehicle to the group.


Vehicle and group are interfaces and truck is a struct.


Creating a group of cars is achieved by having a new
interface implemented. The vehicle interface provides the
functionality of speeding and maintaining the distance
traveled in the odometer.3.


Dependency Inversion Principle
Top layer software modules need not have to rely on bottom
layer modules. Top and bottom layers need to rely on
software interfaces and abstractions. Detailed features are
abstracted out by having abstract classes. This helps in
making the software modular and decoupled. The less
decoupled the software, it helps in enhancing and testing the
features quickly.


Now, let us look at the previous example. In the vehicle, we
need an engine to be added. A new car can be created with
an engine for a specific brand. Let us look at the code now:
dip_principle.go
package main
import "fmt"
type vehicle interface {
getSpeed() float64
getDistanceTravelled() float64
start()
}
type Engine struct {
engineType string
}
type car struct {
brand              string
manufacturing_year int
engine             *Engine
}
func NewCar(brand string, year int, engine *Engine)
*car {
return &car{
brand:              brand,
manufacturing_year: year,
engine:             engine,
}
}
func (Car car) getSpeed() float64 {
return 120
}
func (Car car) getDistanceTravelled() float64 {
return 4000
}
func (Car car) start() {
fmt.Printf(">>> starting the [%s] car ...


\n", Car.brand)
}
func main() {
var veh vehicle
var engine = &Engine{engineType: "V3"}
veh = NewCar("Toyota Tercel", 1997, engine)
fmt.Println("Car's Speed of the Vehicle :",
veh.getSpeed())
fmt.Println("Car's Distance Travelled by
the Vehicle:", veh.getDistanceTravelled())
veh.start()
}
You can now compile and run the dip_principle.go. The
command is as follows:
go run dip_principle.go
The output will be as follows:
Figure 9.7: DIP example output
The output shows how an engine is added to create a car.


Vehicle is an interface and car and engine are structs. When
car is instantiated, brand and engine are passed in the
constructor of the car
Dependency injection
Design principles and patterns are followed by the architects
and lead to building software. Better reusability and
improvement in productivity are observed when design
patterns and principles are applied during software
engineering. Dependency injection is a design pattern
applicable in many tech stacks.


Let us start off by looking at how a computer is assembled
for the order. Most of the customers have in mind the
computer hardware requirements like ram memory, CPU
frequency, and disk space. They are also interested in having
accessories that go with the computer, like a keyboard,
mouse, monitor, and power backup. Many vendors work on
these requirements to assemble the computer based on
these requirements.


While assembling the hardware, vendors are aware of the
motherboards and dependent components that go with
them.


Based on the above example, we can extend the principles
of dependency injection and build software. Some of the
motherboards are loosely coupled and work with
components of different brands and interfaces. Similarly,
software can be built loosely coupled. The components in the
software scenario need to have services and interfaces that
are easily pluggable to other services or containers. There
are many popular containers in Go Lang, like Wire, Inject,
Dig, Dingo, and goioc/di. These containers help in designing
and building components that are easily pluggable into the
containers.


Let us first look at an example of dependency injection
through a constructor. In this case, there is no container. A
media storage system will have services to store media like
video, audio, and images.


di_constructor_example.go
package main
import "fmt"
type Video struct {
content   []byte
storage   VideoStorage
publisher string
}
func NewVideo(ps VideoStorage, publisher string)
*Video {
return &Video{storage: ps, publisher:
publisher}
}
func (video *Video) Load(content string) {
video.content = video.storage.Load(content)
}
func (video *Video) Save(title string) {
video.storage.Save(title, video.content)
}
type VideoStorage interface {
Load(string) []byte
Save(string, []byte)
}
type MediaStorage struct {
video []byte
}
func NewMediaStorage() *MediaStorage {
return &MediaStorage{
video: []byte{},
}
}
func (mStore *MediaStorage) Save(name string,
contents []byte) {
mStore.video = contents
}
func (mStore *MediaStorage) Load(name string)
[]byte {
return mStore.video
}
func (mStore *MediaStorage) Type() string {
return "MediaStorage"
}
func main() {
storage := NewMediaStorage()
video := NewVideo(storage, "STAR MUSIC")
video.Load("Cricket Match NZ vs AUS 1987.


Cricket match happened in Perth and Australia won
the match")
video.Save("Cricket Match NZ vs AUS 1987 ")
fmt.Printf("video : %s \n",
video.publisher)
}
You can now compile and run the
di_constructor_example.go. The command to execute the
code is as follows:
go run di_constructor_example.go
The output will be as follows:
Figure 9.8: DI Constructor example output
In the above example, we have video storage service with
Media Storage and Video Storage interfaces. Video is a
struct. The constructor of the video takes the media storage
interface typed instance. Video struct implements video
storage for saving and loading the video information.


Dependency injection types
The dependency injection method helps in ensuring the
dependencies are loaded and the software is enhanceable
and scalable. The dependent components can be replaced
with any component having the same interface or features.


In the Go Lang tech stack, dependency injection can be done
through structs and interfaces, constructors, setters,
methods, and manual injection. Go Lang dependency
injection has evolved, and many frameworks like Wire, Inject,
Dig, Dingo, good/di, and others support dependency
injection. These frameworks have different features, and
appropriate frameworks can be selected based on user
support, popularity, scalability, usability, and performance.


Using these containers, you can build software that can be
maintained very easily, and new features can be added
within time to market.


There are different types of dependency injection. They are
based on structs/interfaces, constructors, setters, properties,
and manual injection types, as shown in the following figure:
Figure 9.9: Dependency injection types
First, let us look at different injection methods.


Creating structs and interfaces
A class or an object is called in another class for a specific
feature; then, these two classes are dependent on each
other. While creating the class through the constructor, a
new class can be instantiated and passed as a parameter.


This makes these two classes coupled and changing a class
to another class becomes tough. Structs and interfaces come
to the rescue. Structs can be used for creating objects and
interfaces can be used for describing the object behavior.


If two classes have the same interface defined for a feature,
you can interchange them in the parent class easily. In Go
Lang, we use structs to implement the interfaces. Objects
are instantiated and passed on as parameters to the parent
class functions or methods.


Manual dependency injection
Manually creating the dependencies and plugging in the
right one in a class is another option. In this case, it looks
simple and nice, but the coupling becomes higher, and
managing the software becomes tough.


Constructor injection
Dependency injection through the constructor is another
option. This helps in making the injection unmodifiable.


Dependencies can be instantiated and kept ready for
plugging into the right class constructor. You can see the
example as follows:
func main() {
storage := NewMediaStorage()
video := NewVideo(storage, "STAR MUSIC")
}
Setter/method injection
Setter injection is another injection type where the required
dependency can be set on a method. This is better in terms
of the coupling factor. Service classes will have different
functions based on the behaviors to be exposed. The client
invokes the service for a specific function. Service needs to
have the dependent component initialized. Now, let us look
at the client implementation:
func (video *Video) setVideoStorage(vStorage
VideoStorage) {
video.storage = vStorage
}
func main() {
storage := NewMediaStorage()
video := NewVideo(storage, "STAR MUSIC")
vStorage := new(VideoStorage)
video.setVideoStorage(vStorage)
}
Now, let us look at the dependency injection frameworks in
Go Lang.


Dependency injection frameworks
There are many dependency injection frameworks. In Go
Lang, Wire, inject, Dig, Dingo, and goioc/di are the popular DI
frameworks. These frameworks help in creating software that
is loosely coupled and agile in nature.


Google’s Wire
Most of the time, real-life software has many dependencies.


A dependency graph or tree can be created to identify the
level of the dependencies and the root. Changing the
dependent components in complex software is challenging
because of the complex build process and compilation. Build
tools need to have the capability to identify the set of
dependent components and build the packages. Cleaning the
old builds and restarting new builds is a required feature for
build management tools.


Google’s Wire is a popular dependency injection tool that can
easily handle dependency tree management. The framework
initializes these dependencies in the parent classes. Wire has
two important aspects: providers and injectors. Providers are
Go language functions that process the parameters to return
values.


func NewMediaStorage() *MediaStorage {
return &MediaStorage{
video: []byte{},
}
}
The above code shows the provider for Media Storage.


Facebook’s inject
Facebook’s inject framework uses an injection method based
on reflection. Go Lang reflection package is used for
reflecting the required types and constructors to create
dependent objects in the dependency tree. Inject has struct
tags, and these have three different types like inject."",
inject."private", and inject."dev logger".


Uber’s dig
Uber’s dig uses the reflection method for dependency
injection. It has features to build a dependency tree and
bootstrap the required objects during startup. It can handle
singletons in the dependencies. The function parameters
need to implement an interface. Dig has support for methods
of property injection.


Dingo
Dingo is a similar dependency framework to Guice. Struct
tags can be used in this framework. Dependency injection
can also be supported through the request injection type.


Choosing the right framework suitable for you
There are various factors that help identify an appropriate
framework for your software product. The different factors
are listed as follows (Figure 9.4):
Usability
Scalability
Performance
Popularity
User support
Commercial/opensource
Cost
Future-proofed
Figure 9.10: Dependency injection framework selection factors
Conclusion
In this chapter, we have covered topics related to SOLID
principles and dependency injection. Different dependency
injection types were covered, and frameworks supporting
dependency injection were discussed.


The SOLID principles covered in this chapter are Single
Responsibility, Open/Closed, Liskov Substitution, Interface
Segregation, and Dependency Inversion Principles.


Dependency injection can be used to inject dependencies
into the software. Dependencies can be managed in the
software with different types of dependency injection.


Dependency injection can be done through structs and
interfaces, manual, constructor-based, setter, and property-
based injection types. We have covered developers’ popular
dependency injection frameworks like Wire, Inject, Dig,
Dingo, and others.


In the next chapter, the reader will understand containers
and Docker. The reader will be able to develop services that
can be containerized using Docker. Docker Images can be
created and posted on the docker Hub for different Go
services.

