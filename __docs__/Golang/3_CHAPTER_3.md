# 3 CHAPTER 3 Go Console Applications 

error handling, interfaces, typecasting, concurrency, and mutex will be discussed. 

The chapter covers the following topics:

    Console-based app in Go language
    Different input and output formats
    Database interaction
    Data structures

# Console-based app in Go language
Console-based apps are expected to have a menu to guide the user on what kind of outputs can be expected. 

```go

package main

import (
	"bufio"
	"fmt"
	"os"
)

type Customer struct {
	id        int
	name      string
	address   string
	contactno string
}

var customers []Customer

func authenticate() bool {
	var userid, password string
	fmt.Print("Enter Your userid: ")
	fmt.Scan(&userid)
	fmt.Print("Enter Your password: ")
	fmt.Scan(&password)

	if userid == "guest" && password == "guest" {
		fmt.Println("You are authenticated")
		return true
	} else {
		fmt.Println("Your input is wrong. Please enter again")
		return false
	}
}

func showCommandMenu() {
	fmt.Println("1. Display Customers")
	fmt.Println("2. Display Customer by ID")
	fmt.Println("3. Enter Customer Information")
	fmt.Println("4. Update Customer Information")
	fmt.Println("5. Delete Customer")
	fmt.Println("0. Exit")
	fmt.Print("Enter the menu choice id: ")
}

func CommandMenu() {
	var choice int

	showCommandMenu()

	for {
		fmt.Scan(&choice)

		if choice == 0 {
			fmt.Println("You entered 0 for Exiting the Program")
			break
		} else {
			switch choice {
			case 1:
				displayCustomerList(customers)
				fmt.Println("Input 'Enter' to go back to command menu....")
				bufio.NewReader(os.Stdin).ReadBytes('\n')
				showCommandMenu()
			case 2:
				var customerId int
				fmt.Println()
				fmt.Print("Input Customer ID:")
				fmt.Scan(&customerId)
				fmt.Println("\nDisplaying  Customer with ID ", customerId)
				displayCustomerById(customerId, customers)
				fmt.Println("Input 'Enter' to back to command menu....")
				bufio.NewReader(os.Stdin).ReadBytes('\n')
				showCommandMenu()
			case 3:
				fmt.Println("Add a new Customer")
				addNewCustomer(&customers)
				fmt.Println()
				fmt.Println("Input 'Enter' to back to menu....")
				bufio.NewReader(os.Stdin).ReadBytes('\n')
				showCommandMenu()
			case 4:
				var customerId int
				fmt.Print("Input Customer Id: ")
				fmt.Scan(&customerId)
				updateCustomerById(customerId, &customers)
				fmt.Println()
				fmt.Println("Imput 'Enter' to back to menu....")
				bufio.NewReader(os.Stdin).ReadBytes('\n')
				showCommandMenu()
			case 5:
				var customerId int
				fmt.Println()
				fmt.Print("Input Customer Id: ")
				fmt.Scan(&customerId)
				deleteCustomerById(customerId, &customers)
				fmt.Println()
				fmt.Println("input 'Enter' to back to menu....")
				bufio.NewReader(os.Stdin).ReadBytes('\n')
				showCommandMenu()
			default:
				fmt.Println("Input your choice again!")
				showCommandMenu()
			}
		}
	}
}

func displayCustomerList(customers []Customer) {
	for _, customer := range customers {
		fmt.Println("ID :", customer.id)
		fmt.Println("Name :", customer.name)
		fmt.Println("Address :", customer.address)
		fmt.Println("Contact No :", customer.contactno)
		fmt.Println()
	}
}

func displayCustomerById(id int, Customers []Customer) {
	for _, customer := range Customers {
		if customer.id == id {
			fmt.Println("ID :", customer.id)
			fmt.Println("Name:", customer.name)
			fmt.Println("Address:", customer.address)
			fmt.Println("Contact No :", customer.contactno)
			fmt.Println()
		}
	}
}

func addNewCustomer(customers *[]Customer) {
	var customerId int
	consoleReader := bufio.NewReader(os.Stdin)
	fmt.Println()
	fmt.Print("Enter Customer Id : ")
	fmt.Scan(&customerId)
	fmt.Print("Enter Customer Name : ")
	name, _ := consoleReader.ReadString('\n')
	fmt.Print("Enter Customer Address : ")
	address, _ := consoleReader.ReadString('\n')
	fmt.Print("Enter Customer Contact No : ")
	contactno, _ := consoleReader.ReadString('\n')
	Customer_ := Customer{customerId, name, address, contactno}
	*customers = append(*customers, Customer_)
}

func updateCustomerById(id int, customers *[]Customer) {
	var newContactno string
	fmt.Println()
	fmt.Print("Enter the New Contact No of the Customer: ")
	fmt.Scan(&newContactno)
	for i, customer := range *customers {
		if customer.id == id {
			(*customers)[i] =
				Customer{id, customer.name, customer.address, newContactno}
		}
	}
}

func deleteCustomerById(id int, customers *[]Customer) {
	for i, customer := range *customers {
		if customer.id == id {
			*customers =
				append((*customers)[:i], (*customers)[i+1:]...)
		}
	}
}

func CRMConsoleApp() {
	for !authenticate() {
		authenticate()
		break
	}
	customers = append(customers, Customer{id: 1, name: "John Smith", address: "California", contactno: "231456"})
	customers = append(customers, Customer{id: 2, name: "Brad Smith", address: "Boston", contactno: "43212344"})
	CommandMenu()
}

func main() {
	CRMConsoleApp()
}
```

```shell

go run error_example.go
```


> Different input and output formats: The composition of user-defined types is **interface embedding**.
> This is different from `go:embed` directive. 
`go:embed` is used to embed in application binary the folders and files. 

# Text format
First, let us look at text file handling:
```go

package main

import (
	"fmt"
	"log"
	"os"
)

func WriteTextFile(path string) {
	fmt.Println("Writing a Text Format file in Go lang")
	
	text_file, error := os.Create(path)
	if error != nil {
		log.Fatalf("could not create a text file: %s", error)
	}
	
	defer text_file.Close()
	
	length, error :=
		text_file.WriteString("This is to test writing a text formatted file" + " Code demonstrates writing and reading a text file" +
			" Go lang.")
	
	if error != nil {
		log.Fatalf("could not create a text file: %s", error)
	}
	fmt.Println("Text File Name is %s", text_file.Name())
	fmt.Println("Text File Length is %d bytes", length)
}

func FindTextFile(textFile string) {
	fmt.Println("Reading a text format  in Go lang")
	text_data, error := os.ReadFile(textFile)
	if error != nil {
		log.Panicf("cannot reading test from file: %s", error)
	}
	fmt.Println("Text File Name: %s", textFile)
	fmt.Println("Text DataSize: %d bytes", len(text_data))
	fmt.Println("Text Data: %s", string(text_data))
}

func main() {
	path := "input_test_format.txt"
	WriteTextFile(path)
	FindTextFile(path)
}
```

```shell

go run reading_writing_text_format.go
```
The output shows how text-formatted files can be read and written using Go Lang.

# JSON
You can use Unmarshal method on json package to read json data format. 
The Marshal method on json package can be used to write json data formatted file.


Now let us look at the json format file handling:
```go

package main

import (
	"encoding/json"
	"fmt"
	"os"
)

type Customer struct {
	Id      int
	Name    string
	Address string
	Company string
}

func main() {
	customer := Customer{}
	customer.Id = 7832
	customer.Name = "George Smith"
	customer.Address = "California"
	customer.Company = "Dell"
	customerData, _ := json.Marshal(customer)
	fmt.Println(string(customerData))
	
	customer1 := Customer{783, "Thomas Smith", "Boston", "AMD"}
	jsonData, _ := json.Marshal(customer1)
	fmt.Println(string(jsonData))
	
	custData1 := []byte(`{"Id":354,"Name":"John Smith","Addresss":"Denver","Company":"Jockey"}`)
	
	var customer2 Customer
	json.Unmarshal(custData1, &customer2)
	fmt.Println(customer2)
	
	var customer4 Customer
	json.Unmarshal(customerData, &customer4)
	fmt.Println(customer4)
	
	customer5 := Customer{
		Id:      8934,
		Name:    "Kerry Donner",
		Address: "Charlotte",
		Company: "Best Buy",
	}
	json_file, _ := json.MarshalIndent(customer5, "", " ")
	_ = os.WriteFile("customer.json", json_file, 0644)
}
```

```shell

go run reading_writing_json_format.go
```

# XML
You can use UnMarshal() method on xml to read xml bytes to a string. 
Marshal() method on xml package can be used to write an XML file.

```go

package main

import (
	"encoding/xml"
	"fmt"
	"io"
	"os"
)

type Customers struct {
	XMLName   xml.Name   `xml:"customers"`
	Customers []Customer `xml:"customer"`
}
type Customer struct {
	XMLName xml.Name `xml:"customer"`
	//Type    string   `xml:"type,attr"`
	Address string  `xml:"address"`
	Name    string  `xml:"name"`
	Company Company `xml:"company"`
}
type Company struct {
	XMLName  xml.Name `xml:"company"`
	Category string   `xml:"category,attr"`
	Name     string   `xml:"name"`
	Location string   `xml:"location"`
}
type XMLUtil struct {
}

func NewXMLUtil() *XMLUtil {
	return &XMLUtil{}
}
func (xmlUtil *XMLUtil) readXML(path string) {
	xmlFormatFile, error := os.Open("customers.xml")
	if error != nil {
		fmt.Println(error)
	}
	fmt.Println("customers.xml exists and opened")
	defer xmlFormatFile.Close()
	xmlBytes, _ := io.ReadAll(xmlFormatFile)
	var customers Customers
	xml.Unmarshal(xmlBytes, &customers)
	fmt.Println(customers)
	for i := 0; i < len(customers.Customers);
	i++ {
		fmt.Println("Customer Name is " + customers.Customers[i].Name)
		fmt.Println("Customer Address is " + customers.Customers[i].Address)
		fmt.Println("Customer's Company  " + customers.Customers[i].Company.Name)
		fmt.Println("Customer's Company Category is " + customers.Customers[i].Company.Category)
	}
}
func (xmlUtil *XMLUtil) writeXML(path string) {
	customer := Customer{
		Name:    "Jack Donner",
		Address: "Chicago",
		Company: Company{
			Name:     "Marigold",
			Location: "Denver",
			Category: "Silver"},
	}
	file, _ := xml.MarshalIndent(customer, "", " ")
	_ = os.WriteFile(path, file, 0644)
}
func main() {
	var xmlUtil *XMLUtil
	xmlUtil = NewXMLUtil()
	xmlUtil.readXML("customers.xml")
	xmlUtil.writeXML("customers.xml")
}
```

```shell

go run xml_util.go
```

# Database interaction
In Go language, you can use packages or external libraries to reuse code. 
Database `sqlite3` can be connected to the Go Lang program using the `matte/go-sqlite3` package or library. 
In this example, we are going to use Go modules. 

First, let us look at the code for sqlite3 database interaction:
```go

package main

import (
	"database/sql"
	"fmt"
	"log"
	_ "github.com/mattn/go-sqlite3"
)

type DBUtil struct {
}

func NewDBUtil() *DBUtil {
	return &DBUtil{}
}
func (dbUtil *DBUtil) readDB(path string) {
	database, error := sql.Open(path, ":memory:")
	if error != nil {
		log.Fatal(error)
	}
	defer database.Close()
	var version_number string
	error = database.QueryRow("SELECT 	SQLITE_VERSION() 	").Scan(&version_number)
	if error != nil {
		log.Fatal(error)
	}
	fmt.Println(version_number)
}
func main() {
	var dbUtil *DBUtil
	dbUtil = NewDBUtil()
	dbUtil.readDB("sqlite3")
}
```

You can now compile and run the main.go. 
The command to create a module golang-db is shown as follows:
```shell

go mod init golang-db
go mod tidy
```
Then, the module is built using the following command:
```shell
go build
```

The output shows the database version of sqlite3 being used. sqlite3 database is used, and it is very popular for beginners who are learning goland with databases. sqlite3 is a standalone database application.

# Data structures
Basic data structures are `array`, `map`, `slice`, and `struct`. 
We will have advanced data structures like `linked lists`, `sets`, `trees`, and `stacks` in the upcoming chapters. 

A list is a data structure in which every element points to the next element. 
This is called `SinglyLinkedList`. 

A doubly linked list element will have previous and next element pointers.


Now let us look at an example of using mutex:

```go

package main

import (
	"fmt"
)

type Node struct {
	property int
	nextNode *Node
}
type SinglyLinkedList struct {
	headNode *Node
}

func (singlyLinkedList *SinglyLinkedList) AddToTheRoot(property int) {
	var node = &Node{}
	node.property = property
	node.nextNode = nil
	
	if singlyLinkedList.headNode != nil {
		node.nextNode = singlyLinkedList.headNode
	}
	singlyLinkedList.headNode = node
}


func (singlyLinkedList *SinglyLinkedList) CreateNode(property int) *Node {
	var node *Node
	var nodeWith *Node

	for node = singlyLinkedList.headNode; node != nil; node = node.nextNode {
		if node.property == property {
			nodeWith = node
			break
		}
	}
	return nodeWith
}
func (singlyLinkedList *SinglyLinkedList) AddAfterThisElement(nodeProperty int, property int) {
	var node = &Node{}
	node.property = property
	node.nextNode = nil
	var nodeWith *Node 
	nodeWith = singlyLinkedList.CreateNode(nodeProperty)
	
	if nodeWith != nil {
		node.nextNode = nodeWith.nextNode
		nodeWith.nextNode = node
	}
}
func (singlyLinkedList *SinglyLinkedList) GetLastNode() *Node {
	var node *Node
	var lastNode *Node
	for node = singlyLinkedList.headNode; node != nil; node = node.nextNode {
		if node.nextNode == nil {
			lastNode = node
		}
	}
	return lastNode
}
func (singlyLinkedList *SinglyLinkedList) AddToLastNode(property int) {
	var node = &Node{}
	node.property = property
	node.nextNode = nil
	var lastNode *Node
	lastNode = singlyLinkedList.GetLastNode()
	if lastNode != nil {
		lastNode.nextNode = node
	}
}
func (singlyLinkedList *SinglyLinkedList) ListIterator() {
	var node *Node
	for node = singlyLinkedList.headNode; node != nil; node = node.nextNode {
		fmt.Println(node.property)
	}
}
func main() {
	var singlyLinkedList SinglyLinkedList
	singlyLinkedList = SinglyLinkedList{}
	singlyLinkedList.AddToTheRoot(1)
	singlyLinkedList.AddToTheRoot(2)
	singlyLinkedList.AddToLastNode(4)
	singlyLinkedList.AddAfterThisElement(1, 6)
	singlyLinkedList.ListIterator()
}
```

```shell

go run singly_linked_list_example.go
```








