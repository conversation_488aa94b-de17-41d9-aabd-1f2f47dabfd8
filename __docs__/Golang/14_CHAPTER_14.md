# 14 CHAPTER 14

Go Design Patterns—Part 2
Introduction
In this chapter, we look at design patterns like creational,
structural, and behavioral design patterns based on Gang of
Four (GOF) design patterns. Code samples are presented for
the GOF design patterns. The readers will understand object-
oriented design principles using the Go language by learning
GRASP principles. Let us look at behavioral and object-
oriented design patterns in Go:
Figure 14.1: Design patterns in Go Lang
Structure
The chapter covers the following topics
Behavioral design patterns
Object-oriented design patterns
Objectives
In this chapter, we are going to look at design patterns in Go
Lang. Design patterns help resolve issues with solutions that
make the design easy and the applications can scale. The
term GOF patterns is related to a book written by four
different authors. This book was written in 1994 and was
titled Design Patterns: Elements of Reusable Object-Oriented
Software. Go Lang solutions can be built using these design
patterns.


Now, let us look at behavioral design patterns.


Behavioral design patterns
Developers use behavioral design patterns to create objects
of classes that perform tasks. The behavioral design patterns
are as follows:
Chain of responsibility
Command
Interpreter
Iterator
Mediator
Memento
Observer
State
Strategy
Template method
Visitor
First, let us look at the chain of responsibility design pattern.


Chain of responsibility pattern
You can use the chain of responsibility design pattern to
create a chain of objects to pass messages, and each object
processes the request. Let us look at an example where
sections in the shopping store are modeled. The customer
goes through different sections. A section is an interface that
has the process and goto methods. Let us look at the
Customer struct code:
package main
import "fmt"
type Customer struct {
name              string
parkingCompleted  bool
securityCompleted bool
trialCompleted    bool
paymentCompleted  bool
welcomeCompleted  bool
}
type PaymentCounter struct {
next Section
}
func (c *PaymentCounter) process(customer
*Customer) {
if customer.paymentCompleted {
fmt.Println("Customer has paid the
bill")
}
fmt.Println("PaymentCounter getting money
from customer")
}
func (pc *PaymentCounter) goTo(next Section) {
pc.next = next
}
type Section interface {
process(*Customer)
goTo(Section)
}
type SectionBase struct {
nextSection Section
}
The customer goes through the welcome, parking, trial, and
payment sections to complete the shopping journey. Let us
look at the Trial struct code:
type Trial struct {
next Section
}
func (tr *Trial) process(customer *Customer) {
if customer.trialCompleted {
fmt.Println("Customer has completed
the trial")
tr.next.process(customer)
return
}
fmt.Println("Trial performed by the
customer")
customer.trialCompleted = true
tr.next.process(customer)
}
func (tr *Trial) goTo(next Section) {
tr.next = next
}
type Security struct {
next Section
}
func (sec *Security) process(customer *Customer) {
if customer.securityCompleted {
fmt.Println("Customer has completed
the parking")
sec.next.process(customer)
return
}
fmt.Println("Security check performed to
the customer")
customer.securityCompleted = true
sec.next.process(customer)
}
func (sec *Security) goTo(next Section) {
sec.next = next
}
Let us look at the Parking struct code:
Parking struct
type Parking struct {
next Section
}
func (pr *Parking) process(customer *Customer) {
if customer.parkingCompleted {
fmt.Println("Customer has completed
the parking")
pr.next.process(customer)
return
}
fmt.Println("Parking completed by the
customer")
customer.parkingCompleted = true
pr.next.process(customer)
}
func (pr *Parking) goTo(next Section) {
pr.next = next
}
Let us look at the Welcome struct code:
Welcome struct
type Welcome struct {
next Section
}
func (wel *Welcome) process(customer *Customer) {
if customer.welcomeCompleted {
fmt.Println("Customer was
welcomed")
wel.next.process(customer)
return
}
fmt.Println("Customer is getting welcomed")
customer.welcomeCompleted = true
wel.next.process(customer)
}
func (wel *Welcome) goTo(next Section) {
wel.next = next
}
func main() {
pc := &PaymentCounter{}
trial := &Trial{}
trial.goTo(pc)
security := &Security{}
security.goTo(trial)
parking := &Parking{}
parking.goTo(security)
customer := &Customer{name: "John Smith"}
welcome := &Welcome{}
welcome.goTo(parking)
welcome.process(customer)
}
A chain of sections is created to process customers who
come into the shopping store. The customer goes through
different sections, and each section processes the
customer's request for parking, security check, product trial,
and payments.


You can now compile and run the
chain_of_responsibility_example.go. The command is as
follows:
go run chain_of_responsibility
_example.go
The output will be as follows:
Figure 14.2: Chain of responsibility pattern example code output
Now, let us look at the command design pattern.


Command pattern
Developers can use the command design pattern to model
operations that need to be processed. The goal is to have a
loose coupling between the operation and the actual
implementation. Let us look at the implementation of this
pattern in the case of the mobile phone. The command
interface has a process method. Multiple keys on the phone
exist like On, Off, Dial, and the characters/numbers. Each key
is a command to be executed on the mobile phone. The how
part depends on the mobile phone vendor. Command pattern
helps in decoupling the process part from the mobile phone
interface. Let us look at the command interface and
MobilePhone struct implementation in code:
package main
import "fmt"
type Command interface {
process()
}
type MobilePhone struct {
isOn bool
}
func (p *MobilePhone) switchOn() {
p.isOn = true
fmt.Println("The mobile is on")
}
func (p *MobilePhone) switchOff() {
p.isOn = false
fmt.Println("The mobile is off")
}
func (p *MobilePhone) dial() {
if p.isOn {
fmt.Println("The mobile is dialing
the number")
}
}
The phone interface has methods such as switchOn,
switchOff, and dial. Let us look at the Phone interface and
Key struct implementation in the code:
type Phone interface {
switchOn()
switchOff()
dial()
}
type Key struct {
command Command
}
func (b *Key) press() {
b.command.process()
}
type SwitchOnCommand struct {
phone Phone
}
func (sw *SwitchOnCommand) process() {
sw.phone.switchOn()
}
type SwitchOffCommand struct {
phone Phone
}
func (sw *SwitchOffCommand) process() {
sw.phone.switchOff()
}
type DialCommand struct {
phone  Phone
number string
}
func (dial *DialCommand) process() {
dial.phone.dial()
}
func main() {
mobile := &MobilePhone{}
onCommand := &SwitchOnCommand{
phone: mobile,
}
offCommand := &SwitchOffCommand{
phone: mobile,
}
dialCommand := &DialCommand{
phone:  mobile,
number: "321191911",
}
onKey := &Key{
command: onCommand,
}
onKey.press()
dialKey := &Key{
command: dialCommand,
}
dialKey.press()
offKey := &Key{
command: offCommand,
}
offKey.press()
}
The MobilePhone object is created, and commands with
keys are created for the mobile phone interface. Each key is
pressed, and the commands get executed to respond to the
key.


You can now compile and run the command_example.go. The
command is as follows:
go run command_example.go
The output will be as follows:
Figure 14.3: Command pattern example code output
Interpreter pattern
You can use the interpreter design pattern to have a class to
interpret a language.


Let us look at an example where the lexer struct has
methods getNumTokens, process, and the other methods to
analyze the sentences. Let us look at the lexer struct
implementation in code:
package main
import (
"errors"
"fmt"
"strconv"
"strings"
)
type lexer struct {
sentence string
}
func (i *lexer) getNumTokens() []string {
return strings.Split(i.sentence, " ")
}
func (i *lexer) process() int {
sum := 0
tokens := i.getNumTokens()
for k, item := range tokens {
if item == "*" {
fmt.Println(i.getNumTokens())
a, _ :=
strconv.Atoi(string(tokens[k-1]))
b, _ :=
strconv.Atoi(string(tokens[k+1]))
return a * b
}
if item != "+" {
number, _ :=
strconv.Atoi(item)
sum += number
}
}
return sum
}
func keyedStrMap() map[string]string {
var m map[string]string
m = make(map[string]string)
m["+"] = "plus"
return m
}
func (i *lexer) has(s string) bool {
m := keyedStrMap()
if _, ok := m[s]; ok {
return true
}
return false
}
func (i *lexer) checkNormal(s string) error {
if s == "normal" {
return errors.New("non va")
}
i.sentence = s
return nil
}
func (i *lexer) getCountWords() int {
s := i.getNumTokens()
return len(s)
}
Lexer has methods to count the number of words in the
sentences and process the sentence for natural language
grammar. Let us look at the code of how lexer is
instantiated and is used for natural language processing:
func main() {
sentence := "This is a sentence"
lex := lexer{}
lex.checkNormal(sentence)
fmt.Println("No of words in a sentence",
lex.getCountWords())
if lex.getCountWords() != 2 {
fmt.Println("sentence has more than
2 words")
}
sentence = "6 * 8"
lex.checkNormal(sentence)
val := lex.process()
fmt.Println("Product of 6 and 8 is", val)
if val != 48 {
fmt.Println([]string{
"Multiplication between 6
and 8",
"shouldnt be",
strconv.Itoa(val),
})
}
}
In the above example, multiplication is expressed as in
English as the product of two numbers. This expression is
processed by the lexer to give the product.


You can now compile and run the intrepreter_example.go.


The command is as follows:
go run intrepreter _example.go
The output will be as follows:
Figure 14.4: Interpreter pattern example code output
Now, let us look at the iterator design pattern.


Iterator pattern
You can use the iterator design pattern to process a group of
objects in order. This can be done agnostic of the class to
which the object belongs. Let us see an example of an
Iterator interface created to traverse the group of objects
in order. Let us look at the iterator interface and collection
interface code:
package main
import "fmt"
type Iterator interface {
hasNext() bool
getNext() *Customer
}
type Collection interface {
newIterator() Iterator
}
Customer struct and CustomerCollection are defined.


CustomerCollection has newIterator method to create an
Iterator. Let us look at Customer struct and
CustomerCollection struct in code:
type Customer struct {
name string
ssn  string
}
type CustomerCollection struct {
customers []*Customer
}
func (cc *CustomerCollection) newIterator()
Iterator {
return &CustomerIterator{
customers: cc.customers,
}
}
type CustomerIterator struct {
index     int
customers []*Customer
}
func (ci *CustomerIterator) hasNext() bool {
if ci.index < len(ci.customers) {
return true
}
return false
}
func (ci *CustomerIterator) getNext() *Customer {
if ci.hasNext() {
customer := ci.customers[ci.index]
ci.index++
return customer
}
return nil
}
CustomerIterator has methods to traverse an array of
Customers, as follows:
func main() {
customer1 := &Customer{
name: "Andy Gerberg",
ssn:  "321324234",
}
customer2 := &Customer{
name: "Bill Lalelle",
ssn:  "3213223538",
}
customerCollection := &CustomerCollection{
customers: []*Customer{customer1,
customer2},
}
citerator :=
customerCollection.newIterator()
for citerator.hasNext() {
customer := citerator.getNext()
fmt.Println("Customer is ",
customer.name)
}
}
Customer objects are created to form a
customerCollection. CustomerCollection has the method
newIterator to create an iterator. The iterator has methods
hasNext, and getNext.


You can now compile and run the interator_example.go.


The command is as follows:
go run iterator_example.go
The output will be as follows:
Figure 14.5: Iterator pattern example code output
Now, let us look at the mediator design pattern.


Mediator pattern
You can use the mediator design pattern to have loose
coupling between the mediator object and the objects that
interact and communicate with the mediator.


Let us see an example where the mediator interface has
methods canLand and notifyAboutTakeOff:
package main
import "fmt"
type Mediator interface {
canLand(Plane) bool
notifyAboutTakeOff()
}
type Plane interface {
land()
takeOff()
allowLanding()
}
The plane interface has the methods land, takeOff, and
allowLanding. Let us look at AirTrafficController struct
in code:
type AirTrafficController struct {
isRunwayFree bool
planeQueue   []Plane
}
func newAirTrafficController()
*AirTrafficController {
return &AirTrafficController{
isRunwayFree: true,
}
}
func (s *AirTrafficController) canLand(t Plane)
bool {
if s.isRunwayFree {
s.isRunwayFree = false
return true
}
s.planeQueue = append(s.planeQueue, t)
return false
}
func (s *AirTrafficController) notifyAboutTakeOff()
{
if !s.isRunwayFree {
s.isRunwayFree = true
}
if len(s.planeQueue) > 0 {
firstPlaneInQueue :=
s.planeQueue[0]
s.planeQueue = s.planeQueue[1:]
firstPlaneInQueue.allowLanding()
}
}
AirTrafficController struct implements the mediator
pattern and has the planeQueue. Let us look at the
PassengerPlane struct code:
type PassengerPlane struct {
mediator Mediator
}
func (g *PassengerPlane) land() {
if !g.mediator.canLand(g) {
fmt.Println("PassengerPlane:
Landing stopped, waiting")
return
}
fmt.Println("PassengerPlane: Landed")
}
func (g *PassengerPlane) takeOff() {
fmt.Println("PassengerPlane: Leaving")
g.mediator.notifyAboutTakeOff()
}
func (g *PassengerPlane) allowLanding() {
fmt.Println("PassengerPlane: Landing
permitted, Landing")
g.land()
}
PassengerPlane has the mediator object, and messages
were notified to the mediator object to process. Let us look
at how AirTrafficController is instantiated and used:
func main() {
airTrafficController :=
newAirTrafficController()
passengerPlane1 := &PassengerPlane{
mediator: airTrafficController,
}
passengerPlane2 := &PassengerPlane{
mediator: airTrafficController,
}
passengerPlane1.land()
passengerPlane2.land()
passengerPlane1.takeOff()
}
You can now compile and run the mediator_example.go.


The command is as follows:
go run mediator_example.go
The output will be as follows:
Figure 14.6: Mediator pattern example code output
Now, let us look at the memento design pattern.


Memento pattern
Developers can use the memento design pattern to persist
and retrieve an object's state. The following memento
example has the state information as the struct property:
package main
import "fmt"
type Memento struct {
state string
}
func (m *Memento) retrieveState() string {
return m.state
}
type AssociateManager struct {
mementoArray []*Memento
}
func (c *AssociateManager) append(m *Memento) {
c.mementoArray = append(c.mementoArray, m)
}
func (c *AssociateManager) find(index int) *Memento
{
return c.mementoArray[index]
}
type Manager struct {
state string
}
func (e *Manager) newMemento() *Memento {
return &Memento{state: e.state}
}
func (e *Manager) retrieveMemento(m *Memento) {
e.state = m.retrieveState()
}
func (e *Manager) update(state string) {
e.state = state
}
func (e *Manager) retrieveState() string {
return e.state
}
Manager and AssociateManager objects are created. The
application's state is passed on from the branch manager to
the assocManager. The state is retrieved from the
AssociateManager through a memento pattern. Let us look
at how AssociateManager is instantiated and used:
func main() {
assocManager := &AssociateManager{
mementoArray: make([]*Memento, 0),
}
branchManager := &Manager{
state: "state1",
}
fmt.Println("Manager Current State is",
branchManager.retrieveState())
assocManager.append(branchManager.newMemento())
branchManager.update("state2")
fmt.Println("Manager Current State is",
branchManager.retrieveState())
assocManager.append(branchManager.newMemento())
branchManager.update("state3")
fmt.Println("Manager Current State is",
branchManager.retrieveState())
assocManager.append(branchManager.newMemento())
branchManager.retrieveMemento(assocManager.find(1))
fmt.Println("Restored to State is",
branchManager.retrieveState())
branchManager.retrieveMemento(assocManager.find(0))
}
The above example shows how an account application is
processed in a bank from the branch manager to the
associate manager. The branch manager can retrieve the
application's state from the associate manager.


You can now compile and run the memento_example.go. The
command is as follows:
go run memento_example.go
The output will be as follows:
Figure 14.7: Memento pattern example code output
Now, let us look at the observer design pattern.


Observer pattern
You can use the observer design pattern to design a class to
observe the objects of the other class and how the object’s
state changes. Let us look at an example where Observer
and Subject interfaces are defined:
package main
import "fmt"
type Observer interface {
refresh(string)
retrieveIdentifier() string
}
type Subject interface {
addObserver(observer Observer)
removeObserver(observer Observer)
updateAll()
}
Student type is defined with ObserverList as a property.


Student struct implements the Subject interface. Let us
look at the Student struct in code:
type Student struct {
observerList []Observer
name         string
allocated    bool
}
func newStudent(name string) *Student {
return &Student{
name: name,
}
}
func (s *Student) updateAvailability() {
fmt.Println("Teacher is not allocated to
Student", s.name)
s.allocated = true
s.updateAll()
}
func (s *Student) addObserver(o Observer) {
s.observerList = append(s.observerList, o)
}
func (s *Student) removeObserver(o Observer) {
s.observerList =
removeFromlist(s.observerList, o)
}
func (s *Student) updateAll() {
for _, observer := range s.observerList {
observer.refresh(s.name)
}
}
func removeFromlist(observerList []Observer,
observerToRemove Observer) []Observer {
observerListLength := len(observerList)
for i, observer := range observerList {
if
observerToRemove.retrieveIdentifier() ==
observer.retrieveIdentifier() {
observerList[observerListLength-1], observerList[i]
= observerList[i], observerList[observerListLength-
1]
return
observerList[:observerListLength-1]
}
}
return observerList
}
Teacher struct implements the Observer interface. Let us
look at the Teacher struct in code:
type Teacher struct {
id string
}
func (t *Teacher) refresh(itemName string) {
fmt.Println("Sending email to teacher",
t.id)
}
func (t *Teacher) retrieveIdentifier() string {
return t.id
}
func main() {
student := newStudent("Nick Jordan")
teacher1 := &Teacher{id:
"<EMAIL>"}
teacher2 := &Teacher{id:
"<EMAIL>"}
student.addObserver(teacher1)
student.addObserver(teacher2)
student.updateAvailability()
}
The Student and Teacher objects are created. Teachers are
added to the students as observers. The student can notify
and update his availability by sending an email.


You can now compile and run the observer_example.go.


The command is as follows:
go run observer_example.go
The output will be as follows:
Figure 14.8: Observer pattern example code output
Now, let us look at the state design pattern.


State pattern
You can use a state design pattern to create an object and
modify the object’s state. This pattern helps in changing the
object’s behavior. The following code shows the State
interface, which has requestMoney, insertMoney, and
dispenseMoney methods. ATM struct has multiple states and
money as the property:
package main
import "fmt"
type State interface {
requestMoney(int)
insertMoney(int)
dispenseMoney(int)
}
type ATM struct {
hasMoney       State
noMoney        State
MoneyRequested State
presentState State
money int
}
func newATM(money int) *ATM {
atm := &ATM{
money: money,
}
hasMoneyState := &HasMoneyState{
atm: atm,
}
moneyRequestedState :=
&RequestedMoneyState{
atm: atm,
}
noMoneyState := &NoMoneyState{
atm: atm,
}
atm.setState(hasMoneyState)
atm.MoneyRequested = moneyRequestedState
atm.hasMoney = hasMoneyState
atm.noMoney = noMoneyState
return atm
}
ATM implements the State interface by having methods
requestMoney, insertMoney, and dispenseMoney. Let us
look at the ATM implementation of the state interface in
code:
func (atm *ATM) requestMoney(money int) {
atm.presentState.requestMoney(money)
}
func (atm *ATM) insertMoney(money int) {
atm.presentState.insertMoney(money)
}
func (atm *ATM) dispenseMoney(money int) {
atm.presentState.dispenseMoney(money)
}
func (atm *ATM) setState(s State) {
atm.presentState = s
}
func (atm *ATM) incrementMoney(money int) {
fmt.Println("Adding money", money)
atm.money = atm.money + money
if atm.money > 0 {
atm.setState(atm.hasMoney)
}
}
func (atm *ATM) decrementMoney(money int) {
fmt.Println("Decreasing money", money)
if money <= atm.money {
atm.money = atm.money - money
if atm.money == 0 {
atm.setState(atm.noMoney)
}
} else {
fmt.Println("Not enough money to
decrement")
}
}
HasMoneyState, NoMoneyState, and RequestedMoneyState
structs are defined in the following code. Each of these
structs implements a State interface:
type HasMoneyState struct {
atm *ATM
}
func (i *HasMoneyState) requestMoney(money int) {
if money <= i.atm.money {
fmt.Println("Money in ATM",
i.atm.money)
} else {
fmt.Println("Not Enough money in
ATM", i.atm.money)
}
}
func (i *HasMoneyState) dispenseMoney(money int) {
fmt.Println("Money dispense in progress",
money)
i.atm.decrementMoney(money)
}
func (i *HasMoneyState) insertMoney(money int) {
fmt.Println("Money is there- not added",
i.atm.money)
}
type NoMoneyState struct {
atm *ATM
}
func (i *NoMoneyState) requestMoney(money int) {
if money <= i.atm.money {
fmt.Println("Money in ATM")
} else {
fmt.Println("Not Enough money in
ATM")
}
}
func (i *NoMoneyState) dispenseMoney(money int) {
fmt.Println("No money")
}
func (i *NoMoneyState) insertMoney(money int) {
fmt.Println("Money is added now", money)
i.atm.incrementMoney(money)
}
type RequestedMoneyState struct {
atm *ATM
}
func (i *RequestedMoneyState) requestMoney(money
int) {
fmt.Println("Money requested", money)
}
func (i *RequestedMoneyState) dispenseMoney(money
int) {
fmt.Println("dispense will start soon")
}
func (i *RequestedMoneyState) insertMoney(money
int) {
fmt.Println("Money is requested and it
cannot be added")
}
ATM is created with the newATM method by specifying the
initial money as ten units. Let us look at how ATM is
instantiated and used:
func main() {
atm := newATM(10)
atm.requestMoney(10)
atm.dispenseMoney(10)
atm.insertMoney(500)
}
In the above example, a request for ten units of money is
sent. ATM dispenses ten units of money. 500 units of money
are inserted in the ATM.


You can now compile and run the state_example.go. The
command is as follows:
go run state_example.go
The output will be as follows:
Figure 14.9: State pattern example code output
Now, let us look at the strategy design pattern.


Strategy pattern
You can use the strategy design pattern to create a group of
techniques, approaches, and methods and implement any of
them when required. Let us see an example where the
DeductionStrategy interface is defined with getDeduction
methods. Product struct has deductionstrategy as the
property. Let us look at DeductionStrategy interface in the
code:
package main
import "fmt"
type DeductionStrategy interface {
getDeduction(int) int
}
type Product struct {
name              string
deductionStrategy DeductionStrategy
cost              int
}
func (prod *Product) getCost() {
prod.cost = prod.cost -
prod.deductionStrategy.getDeduction(prod.cost)
fmt.Println("Cost of the Product after
deduction", prod.cost)
}
FixedDeduction and PropDeduction strategies are created
with fixed cost deduction and proportionate deduction,
respectively. Let us look at FixedDeduction struct in code:
type FixedDeduction struct {
}
func (deduction *FixedDeduction) getDeduction(cost
int) int {
return 10
}
type PropDeduction struct {
}
func (deduction *PropDeduction) getDeduction(cost
int) int {
return int(10 * cost / 100)
}
func main() {
fixedDeduction := &FixedDeduction{}
propDeduction := &PropDeduction{}
product1 := &Product{name: "IPad",
deductionStrategy: propDeduction, cost: 30000}
product1.getCost()
product2 := &Product{name: "Diary",
deductionStrategy: fixedDeduction, cost: 30}
product2.getCost()
}
FixedDeduction and PropDeduction objects are created.


Product object is created with name and proportionate and
fixed deduction. In both cases, getCost method returns the
calculated cost after deduction.


You can now compile and run the strategy_example.go.


The command is as follow:
go run strategy_example.go
The output will be as follows:
bhagvanarch@Bhagvans-MacBook-Air Behavioural Design
Patterns % go run strategy_example.go
Cost of the Product after deduction 27000
Cost of the Product after deduction 20
bhagvanarch@Bhagvans-MacBook-Air Behavioural Design
Patterns %
Now, let us look at the template method design pattern.


Template method pattern
You can use the template design pattern to create a class to
define the technique. Subclasses of the template class can
have different implementations. The ContentManager
interface has a getContent method. The document
implements the interface. Let us look at the ContentManger
interface in the code:
package main
import (
"fmt"
"strings"
)
type ContentManager interface {
getContent() string
}
type Document struct {
content string
}
func (doc *Document) getContent() string {
return doc.content
}
The template interface has methods intro, desc, and
placeholder:
type Template interface {
intro() string
desc() string
placeHolder(ContentManager) string
}
FixedTemplate and DynamicTemplate implement the
Template interface, but they implement different methods,
such as intro, desc, and placeholder:
type FixedTemplate struct{}
func (t *FixedTemplate) intro() string {
return "This is the introduction section"
}
func (t *FixedTemplate) desc() string {
return "This is the description section"
}
func (t *FixedTemplate) placeholder(cm
ContentManager) string {
return strings.Join(
[]string{
t.intro(),
t.desc(),
cm.getContent(),
},
" ",
)
}
type DynamicTemplate struct {
intro_text string
desc_text  string
}
func (a *DynamicTemplate) intro() string {
return a.intro_text
}
func (a *DynamicTemplate) desc() string {
return a.desc_text
}
func (a *DynamicTemplate) placeholder(cm
ContentManager) string {
return strings.Join(
[]string{
a.intro(),
a.desc(),
cm.getContent(),
},
" ",
)
}
func main() {
doc := &Document{content: "This is a
detailed section"}
fixed := &FixedTemplate{}
document := fixed.placeholder(doc)
fmt.Println("Fixed Template based document
is", document)
dynamic := &DynamicTemplate{intro_text:
"Dynamic introduction", desc_text: "Dynamic
Description"}
dynamic_text := dynamic.placeholder(doc)
fmt.Println("Dynamic Template based
document is", dynamic_text)
}
In the example above, a document is created with different
templates, namely, fixed and dynamic. The document object
has the initial text, which is passed on to the template to
change the document to the specified template.


You can now compile and run the template_example.go.


The command is as follows:
go run template_example.go
The output will be as follows:
Figure 14.10: Template pattern example code output
Now, let us look at the visitor design pattern.


Visitor pattern
Developers use the visitor pattern to segregate the
technique from the object by defining a new object from a
class that defines the technique. This also helps to
incorporate multiple techniques for the same object through
visitors. Let us look at the following example where the
Vistior interface is defined with getTruckVelocity and get
CarVelocity methods:
package main
import (
"fmt"
)
type Visitor interface {
getTruckVelocity(*Truck) int
getCarVelocity(*Car) int
}
Truck and Car structs are defined with the acceptVisitor
method:
type Truck struct {
highestSpeed int
}
func (t *Truck) acceptVisitor(v Visitor) {
fmt.Println("Velocity of the truck is",
v.getTruckVelocity(t))
}
func (t *Truck) getVehicleType() string {
return "Truck"
}
func (t *Truck) getTruckVelociy() int {
return t.highestSpeed
}
type Car struct {
highestSpeed int
}
func (c *Car) acceptVisitor(v Visitor) {
fmt.Println("Velocity of the Car is:",
v.getCarVelocity(c))
}
func (c *Car) getVehicleType() string {
return "Car"
}
VehicleVelocity is the visitor class that uses truck and car
velocity methods:
type VehicleVelocity struct {
}
func (v *VehicleVelocity) getTruckVelocity(t
*Truck) int {
return t.highestSpeed
}
func (v *VehicleVelocity) getCarVelocity(c *Car)
int {
return c.highestSpeed
}
func main() {
truck := &Truck{highestSpeed: 160}
car := &Car{highestSpeed: 120}
velocity := &VehicleVelocity{}
truck.acceptVisitor(velocity)
car.acceptVisitor(velocity)
}
In the example above, VehicleVelocity visitor separates
the speed calculation like an odometer in a truck and car.


You can now compile and run the visitor_example.go. The
command is as follows:
go run visitor_example.go
The output will be as follows:
Figure 14.11: Visitor pattern example code output
Now, let us look at the object-oriented design patterns.


Object-oriented design patterns
While developing software, different methodologies like OOD,
DDD and other methodologies are used. Object-oriented
design (OOD) focuses on the classes, their responsibilities,
and the collaboration between the classes. General
Responsibility Assignment Software Patterns (GRASP)
are the principles that help in Object-oriented design.


GRASP patterns are based on the following principles:
Creation of objects
Information expertise
Low coupling
High cohesion
Controller
Polymorphism
Abstraction
Pure fabrication
Now, let us look at these principles in detail.


GRASP patterns
GRASP principles are used in object-oriented design
methodology. These principles help design systems that are
flexible, adaptable, resilient, fail-proof, maintainable, and
extensible. Future requirements can be easily added to the
software's upcoming releases. The principles are as follows:
Creator: This principle concerns the class that creates
objects. The creator class needs to be decoupled from
the objects of the classes that are created. The creator
class can be an aggregate or a composite. This
principle helps determine which class takes the
responsibility of object creation.


Information expertise: This principle emphasizes the
importance of the class having information related to
the task to be processed. The goal is to have
cohesiveness and lessen dependencies in the classes.


The class needs to be responsible for a single task and
have all the information related to the task.


Low coupling: This principle is based on low coupling
between the classes. Classes need to have fewer
interconnections. The goal is to have a modular and
flexible design. This makes the software easy to
maintain. Change requests can be handled easily, and
new
features
can
be
added
easily,
improving
reusability.


High cohesion: The high cohesion principle states
that the class design needs to factor in encapsulation
and a single responsibility for a class. This will help
improve reuse and handle changes in the future. Each
class needs to have a well-defined purpose and focus.


Controller: Developers use the controller principle to
model classes mediating between the UI and the
business logic layer. These controller classes help in
processing the client requests from the UI to invoke
business layer classes and respond to the user
interface with a response. This helps in decoupling the
different aspects of the design. The controller class is
responsible for system operations encapsulation.


Polymorphism: The polymorphism principle is an
object-oriented design methodology principle. Multiple
classes can have a single interface. The goal is to have
a design that is adaptable and extensible. This
principle helps reduce coupling and improve reuse.


Because of the polymorphism principles, subclasses
can be easily referred to as the superclass.


Abstraction: The Single Layer of Abstraction
(SLAP) principle encourages abstraction in the design
and a layered design to handle presentation, business,
external interface integration, data storage, and data
persistence features.


Pure fabrication: The pure fabrication principle is
used to create classes that are not related to the
context. These classes are helper classes and help
decouple the functionality from the domain objects.


They also help improve cohesion and reduce coupling
with external systems. Some of the helper classes can
be related to input and output handling, data format
transformation, and data persistence.


Conclusion
In this chapter, we have covered topics related to Gang of
Four design patterns and object-oriented design patterns.


Behavioral design patterns were discussed in detail with
context and examples. Object-oriented design principles and
GRASP patterns were presented in detail.


In the next chapter, the reader will know how to develop Go
applications that are responsive and performant. Different
performance tuning and optimization patterns are presented
with code samples to the reader. The readers will understand
the performance tuning and optimization techniques while
building web applications.

