# 7 CHAPTER 7

Go Real Life Applications—
CRM
Introduction
In this chapter, the reader will learn how to build a real-life
application for customer relationship management
(CRM). CRM application will be a web application that will
have the capability to maintain customer data and run
campaigns for segments or a list of customers by geography
or vertical. Now, let us look at what is covered in CRM
application:
Figure 7.1: CRM Application components
Structure
The chapter covers the following topics:
CRM application in Go
Unit Testing the REST API
Objectives
By the end of this chapter, we will build a customer
relationship management application using the Go language.


The Beego framework will be used to build the REST API and
web application. The readers will be presented with
examples of unit testing Rest API in the Go language.


CRM application in Go
We will be building a CRM application using Go. This
application will consist of the following modules:
Customer management
Lead generation
Customer segmentation
Loyalty management
Merchant management
Campaign management
CRM application consists of creating customers and
managing customer data. Campaigns will be created and
targeted for publishing to the customers. The leads are
gathered as a list of customers or segments of customers.


Customer segments are created by attributes like age,
gender, salary range, or geography. When the customer likes
a product and buys the product, a transaction is logged in
the back office of a retail store or an e-commerce company.


The transactions are processed to check if the lead has been
realized and turned into revenue. This process helps in
assessing the lead conversion. Transactions can also be
checked for aberration to identify the customer’s behavior.


The other important feature is the loyalty points, which have
different levels like gold, silver, and bronze levels. The
customer buys the products, and loyalty points are stored in
the system to track the customer's progress. Offers are
targeted based on loyalty to improve the customer's
purchasing rate. The discounted offers are shared based on
where the customer is in terms of the loyalty points.


Companies bombastically frequently announce loyal
customers and convert leads from the CRM portal.


Let us start creating the REST API for this system.


REST API
Beego framework excels in the Go ecosystem due to its rich
feature set and ease of use. It combines the power of a
robust MVC architecture with tools for rapid development,
like built-in ORM, powerful caching mechanisms, and RESTful
support, reducing boilerplate code and speeding up the
development process. Moreover, Beego's automatic routing
and strong community support make it a favorite for building
scalable web applications. Its performance optimization and
flexibility in integrating with other Go libraries set it apart
from many other frameworks.


We will be using the Beego web framework to develop the
CRM web application and REST API. Beego framework is an
open-source framework that has application tools, an object
relationship mapping framework, and other packages. ORM
framework helps cut down the ordeal of developing the CRM
relational schema and mapping it to object classes and
names of the attributes. Beego is used to build CRM REST
APIs, which can be integrated with open-source JavaScript
frontend frameworks. We can build CRM Beego web
applications using the Beego and SQLite database. Beego
framework helps in developing web apps at an affordable
cost.


First, let us look at the code for the Beego REST API. The
code is as follows:
main.go
package main
import (
"fmt"
"github.com/gorilla/handlers"
"go_beego_rest_api/pkg/db"
handler "go_beego_rest_api/pkg/handlers"
"log"
"net/http"
"github.com/gorilla/mux"
)
func main() {
r := mux.NewRouter()
DB := db.InitializeDB()
r.HandleFunc("/customers",
handler.GetCustomers(DB)).Methods("GET")
r.HandleFunc("/create",
handler.CreateCustomer(DB)).Methods("POST")
r.HandleFunc("/update",
handler.UpdateCustomer(DB)).Methods("PUT")
r.HandleFunc("/delete",
handler.DeleteCustomer(DB)).Methods("DELETE")
fmt.Println("Server at 8080")
log.Fatal(http.ListenAndServe(":8080",
handlers.CORS()(r)))
}
The following figure shows the Beego Web app directory
structure:
Figure 7.2: Code structure
The above main.go has the code for initializing the ORM and
registering the model. Beego is started in the main method.


Now, let us look at the database code:
db.go
package db
import (
"fmt"
"github.com/jinzhu/gorm"
_
"github.com/jinzhu/gorm/dialects/postgres"
)
func InitializeDB() *gorm.DB {
db, err := gorm.Open("postgres",
"user=newuser password=newuser dbname=crm
sslmode=disable")
if err != nil {
fmt.Println(err)
} else {
fmt.Println("DB connected!")
}
return db
}
Handler.go has the code for the tableName definition and the
object class specification. Now, let us look at the handler
code:
handler.go
package handler
import (
"encoding/json"
"fmt"
"net/http"
"github.com/jinzhu/gorm"
)
type CustomerBody struct {
Name string `json"name"`
}
type Customer struct {
Id      int    `json:"id"`
Name    string `json:"name"`
Mobile  string `json:"mobile"`
Address string `json:"address"`
}
func GetCustomers(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var customers []Customer
_ =
db.Table("customer").Select("id,
name,mobile,address").Scan(&customers)
json.NewEncoder(w).Encode(customers)
}
}
func CreateCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var RequestBody CustomerBody
json.NewDecoder(r.Body).Decode(&RequestBody)
_ =
db.Table("customer").Create(&RequestBody)
fmt.Println("Created Customer")
json.NewEncoder(w).Encode(RequestBody)
}
}
func UpdateCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var PutBody Customer
json.NewDecoder(r.Body).Decode(&PutBody)
_ =
db.Table("customer").Where("id=?",
PutBody.Id).Update("name",
PutBody.Name).Scan(&PutBody)
fmt.Printf("Updated Customer with
id %d\n", PutBody.Id)
json.NewEncoder(w).Encode(PutBody)
}
}
func DeleteCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var DeleteBody Customer
json.NewDecoder(r.Body).Decode(&DeleteBody)
_ =
db.Table("customer").Delete(&DeleteBody)
fmt.Printf("Deleted Customer with
id %d\n", DeleteBody.Id)
json.NewEncoder(w).Encode(DeleteBody)
}
}
Now, let us look at the database script to create a table in
the Postgres CRM database. You can use the following
commands to create a Postgres database CRM and users:
psql -h localhost -d postgres
----
ALTER USER postgres PASSWORD 'postgres';
CREATE USER newuser with PASSWORD 'newuser'
CREATEDB;
select * from users;
\du
\l
\q
-----
createdb crm
psql -h localhost -d postgres
\c crm
CREATE TABLE "customer" (
"id"  SERIAL,
"name" varchar(200) NOT NULL,
"mobile" varchar(100),
"address" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
\dt
\du
alter role newuser superuser;
create user newuser with password 'newuser';
grant all privileges on database crm to newuser;
alter role newuser superuser;
Database
CREATE TABLE "customer" (
"id" serial,
"name" varchar(200) NOT NULL,
"mobile" varchar(100),
"address" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
create user newuser with password 'newuser';
grant all privileges on database crm to newuser;
Now, let us look at the routes configured in index.go.


The following table shows the routes/paths mentioned in the
index.go:
Action
Controller
method
Http
method
URL route
Description
Create
Add
POST
/create
Create a New
Customer
Read
getAll
GET
/customers
Retrieve list of
customers
Delete
Delete
DELETE
/delete
Delete a
Customer
Update
Update
PUT
/update
Update a
Customer
Table 7.1: HTTP Methods—REST API methods
You can run the beego REST API server by using the following
commands:
go mod init go_beego_rest_api
go mod tidy
The output of the rest of the API server execution is shown as
follows:
Figure 7.3: REST API Server execution output
Unit testing the REST API
In this section, we will investigate how Postman is used for
unit testing the REST API. You can perform the unit testing of
the API using postman. Now, let us look at the Create
Customer API.


Create Customer
Figure 7.4: Create Customer API call
Postman can be used to test the REST API. In Figure 7.3, the
customer is created with a post-method-based REST API call.


Now, let us look at the Get Customers API.


Get Customers
Figure 7.5: Get Customers API Call
Figure 7.5 shows all customers by retrieving the results from
a GET method REST API call. Now, let us look at the Update
Customer REST API code.


Update Customer
Figure 7.6: Update Customer API call
Figure 7.6 shows how Postman is used for updating the
customer. PUT method-based REST API is used to update the
customer in the CRM portal. Now, let us look at the Delete
Customer API code.


Delete Customer
Figure 7.7: Delete REST API call
DELETE Method-based REST API is called using Postman for
deleting a customer, as shown in Figure 7.7. Now, let us look
at the Get Customers After Delete API call.


Get Customers After Delete
Figure 7.8: GET Customers REST API call after delete
Figure 7.8 shows the list of customers after update and
delete operations.


Web app
Now, let us look at the web application. The web application
is built using the React framework.


You can build it using these steps:
1. Create an index.html
2. Create an index.js
3. Create an example.js
4. Create a home.jsx page
5. Create a AddCustomer.jsx page
6. Create an AddCustomer.css
7. Create a Logout page.


First, let us look at the index.html webpage
implementation:
Index.html—Webpage
<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-
width, initial-scale=1, shrink-to-fit=no">
<meta name="theme-color" content="#000000">
<link rel="manifest"
href="%PUBLIC_URL%/manifest.json">
<link rel="shortcut icon"
href="%PUBLIC_URL%/favicon.ico">
<title>CRM Portal</title>
</head>
<body>
<div id="root"></div>
</body>
</html>
Now, let us look at index.js. This JavaScript file renders the
application by loading the root element defined in
example.js:
index.js
import React from 'react';
import ReactDOM from 'react-dom';
import App from './example';
ReactDOM.render(
<App />,
document.getElementById('root')
);
example.js
import React from "react";
import {
BrowserRouter as Router,
Switch,
Route,
Link
} from "react-router-dom";
import AddCustomer from './AddCustomer';
import Logout from './Logout';
import Home from './Home';
export default function BasicExample() {
return (
<Router>
<div>
<ul>
<li>
<Link to="/home">Home</Link>
</li>
<li>
<Link to="/addCustomer">Add
Customer</Link>
</li>
<li>
<Link to="/logout">Logout</Link>
</li>
</ul>
<hr />
{}
<Switch>
<Route exact path="/home">
<Home />
</Route>
<Route path="/addCustomer">
<AddCustomer />
</Route>
<Route path="/logout">
<Logout />
</Route>
</Switch>
</div>
</Router>
);
}
In the above file, different routes are defined for Home,
AddCustomer, and Logout. Now, let us look at the Home page:
Home.jsx
import { Link } from "react-router-dom";
import React from "react";
class Home extends React.Component {
constructor(props) {
super(props);
this.state = {
jokes: []
};
this.serverRequest =
this.serverRequest.bind(this);
}
serverRequest() {
fetch('http://localhost:8080/customers').then(respo
nse => {
return response.json();
}).then(json => {
console.log("json",json)
this.setState({ jokes: json});
});
}
componentDidMount() {
this.serverRequest();
}
render() {
return (
<div className="container">
<br />
<h2>CRM</h2>
<br />
<p>List of Customers</p>
<div className="row">
<div className="container">
<table border="1">
<tr>
<th>Name</th><th> Mobile</th>
<th>Address</th>
</tr>
{this.state.jokes.map((item, index) =>
(
<tr border="1">
<td border="1">
{item.name} </td>
<td border="1">
{item.mobile}</td>
<td border="1">
{item.address}</td>
</tr>
))}
<br />
</table>
</div>
<a href="/add">Add New
Customer</a>
</div>
</div>
);
}
}
export default Home;
The AddCustomer page has the form to create a customer.


This page talks about the REST API for the creation of a
customer:
AddCustomer.jsx
import { useState } from "react";
import React from "react"
import "./AddCustomer.css";
function AddCustomer() {
const [name, setName] = useState("");
const [mobile, setMobile] = useState("");
const [address, setAddress] = useState("");
const [message, setMessage] = useState("");
let handleSubmit = async (e) => {
e.preventDefault();
try {
let res = await
fetch("http://localhost:8080/create", {
method: "POST",
body: JSON.stringify({
name: name,
mobile: mobile,
address: address
}),
});
let resJson = await res.json();
if (res.status === 200) {
setName("");
setAddress("");
setMessage("User created successfully");
} else {
setMessage("Some error occured");
}
} catch (err) {
console.log(err);
}
};
return (
<div className="container">
<br />
<div className="row">
<div className="container">
<form onSubmit={handleSubmit}>
<div class="form-group">
<label for="name">Customer name</label>
<input value={name}
placeholder="Name"
onChange={(e) => setName(e.target.value)}
class="form-control" tabindex="1" />
</div>
<div class="form-group">
<label for="mobile">Customer Mobile：
</label>
<input value={mobile}
placeholder="Mobile"
onChange={(e) =>
setMobile(e.target.value)} class="form-control"
tabindex="2" />
</div>
<div class="form-group">
<label for="address">Address：</label>
<input value={address}
placeholder="Address"
onChange={(e) =>
setAddress(e.target.value)} class="form-control"
tabindex="3" />
</div>
<button>Add Customer</button>
<div className="message">{message ? <p>
{message}</p> : null}</div>
</form>
</div>
</div>
</div>
);
}
export default AddCustomer;
The addCustomer page has the CSS defined as
AddCustomer.css:
AddCustomer.css
.AddCustomer {
display: flex;
justify-content: center;
margin-top: 5rem;
}
input {
display: block;
width: 20rem;
height: 2rem;
padding: 0.5rem;
font-size: 1.1em;
font-weight: 500;
margin-bottom: 2rem;
}
button {
border: none;
padding: 1rem;
width: 21.2rem;
font-size: 1.2em;
border-radius: 0.2rem;
cursor: pointer;
}
button:hover {
background-color: #c5c5c5;
}
.message {
font-size: 1.2em;
text-align: center;
color: #36a936;
}
Logout.jsx
import React from "react";
class Logout extends React.Component {
constructor(props) {
super(props);
this.logout = this.logout.bind(this);
}
logout() {
localStorage.removeItem("id_token");
localStorage.removeItem("access_token");
localStorage.removeItem("profile");
//location.reload();
}
render() {
return (
<div>
<h1>Logout</h1>
<span className="pull-right">
<a onClick={this.logout}>Log out</a>
</span>
</div>
);
}
};
export default Logout;
You can now compile and run the react application with the
following command:
npm install
npm start
You can access the react web app at
http://localhost:3000/.


The following figure shows the web application:
Figure 7.9: Customers List
Figure 7.9 shows the screenshot of all customers loaded by
the REST API for the command get all customers. Now, let us
look at the AddNewCustomer page:
Figure 7.10: Add New Customer page
Figure 7.10 shows the form for adding new customers to the
CRM portal. Now let us look at the output for the customer’s
list call:
Figure 7.11: List of Customers page
After adding a new customer, the user can return to the CRM
portal and see the list of customers. Now, let us look at the
message shown at the bottom of the page:
Figure 7.12: User message at the bottom of the page
After the customer is created, a message on the page shows
the successful result of creating a user. This message helps
the user be cognizant of the result because of the action
submitted.


Conclusion
In this chapter, we have covered topics related to building
CRM applications using Go Lang. CRM application is built
using Beego Rest API and React framework. CRM application
is built using Beego framework-based REST API interacting
with SQLite database. REST API can be developed using
Beego framework. Beego Framework has the capabilities of
REST APIs, ORM, and developing web application
frameworks. Postman can be used for unit testing the REST
API to create, update, delete, and get all customers. The web
application can be built using the React framework with html
pages interacting with Beego framework-based REST API.


In the next chapter, readers will learn how to build
concurrent applications using Go. The readers will know how
to use Go routines, channels, and concurrency using Go. The
reader will understand concurrency and parallelism
principles.


Join our book’s Discord space
Join the book's Discord Workspace for Latest updates, Offers,
Tech happenings around the world, New Release and
Sessions with the Authors: <AUTHORS>