# 16 CHAPTER 16 Go Web App Blueprints

principles to build scalable web applications. 
build responsive web applications that are performant. 
Let us look at Go web application recipes, architecture, and design principles to build scalable and responsive web applications:

    Web app in Go recipes
    Building scalable and responsive web app
    Web app design principles
    Web app architecture principles

particularly well-suited for building scalable and high-performance web applications.

The Go language offers a variety of frameworks and tools that streamline the development process, making it easier for developers to create well-structured and maintainable web applications.

Go for web development is its powerful standard library, which includes a built-in HTTP server. This allows developers to quickly set up web servers without the need for external dependencies.

Additionally, Go's concurrency model, based on goroutines and channels, enables efficient handling of multiple requests simultaneously, making it ideal for high-traffic web applications.
The language's simplicity and readability also contribute to faster development cycles and easier code maintenance.

Blueprints for Go-based web applications typically follow a Model-View-Controller (MVC) architecture, which helps in organizing the codebase into distinct layers. 
The model layer handles the data and business logic, the view layer manages the user interface, and the controller layer processes incoming requests and returns responses. 

This separation of concerns ensures that the application is modular and easier to manage. Frameworks like Gin, Echo, and Fiber provide additional features and utilities that enhance the
development experience, such as routing, middleware support, and template rendering.

Another significant aspect of Go web app blueprints is the emphasis on performance and scalability. Go's efficient memory management and garbage collection, combined with its ability to compile to native machine code, result in applications that are both fast and resource-efficient. This makes Go an excellent choice for building microservices and other distributed systems where performance is critical.

Moreover, Go's strong typing and static analysis tools help catch errors early in the development process, leading to more reliable and secure applications.

In addition to the technical benefits, Go's growing ecosystem and active community provide ample resources for developers. Numerous libraries and packages are available to extend the functionality of Go web applications, from database connectors and authentication modules to real-time communication tools and API clients. The community also offers extensive documentation, tutorials, and forums where developers can seek advice and share knowledge.

This collaborative environment fosters continuous learning and improvement, making Go a vibrant and supportive platform for web development.

Overall, Go language-based web app blueprints offer a comprehensive and efficient approach to building modern web applications. With its powerful standard library, strong concurrency model, and supportive community, Go provides developers with the tools they need to create high-
performance, scalable, and maintainable web applications.

Whether you are building a simple website or a complex distributed system, Go's simplicity and robustness make it an excellent choice for web development. 

# Web app in Go recipes
Golang.org was the website from where the language was named. 
Go Lang is the same as C language without any drawbacks of C language. Developers like Go Lang over C as the Go Lang programs are memory-safe, and concurrency can be implemented easily in data engineering, artificial intelligence, data processing, networking, and backend apps. Go Lang has garbage collection and static typing features. Let us look at different web frameworks
based on the Go tech stack:

To build web applications, Go Lang is simple and easy to use.

There are various frameworks that help in building web pages and backend services. Go language packages are available for encryption, file handling, networking, and content serving. The Go language web app can be developed on one of the operating systems and deployed on other operating systems easily with recompilation. This makes the web app portable and easy to deploy on multiple clouds.

There are many web app frameworks that developers can use. They are as follows:
- _**Gin**_ REST framework is based on an HTTP Router, which provides features like routing and form validation. 
- use GORM to store data in the relational database. 

- _**Beego**_ is an open-source framework that has application tools, an object relationship mapping framework, and other packages. ORM framework helps in cutting down the ordeal of developing the relational schema and mapping it to object classes and names of the attributes. 
Beego is used to build REST APIs, which can be integrated with open-source JavaScript frontend frameworks. We can build a Beego web application using the Beego and SQLite database. 
- _**Kit**_ provides the capability to build web applications interacting with databases and microservices. The services can be interoperable, built through the web pages to create an application. 
- **_Echo_**, provides features for HTTP request handling, building microservices based on REST protocol, and securing the web application with SSL. 
- _**Fiber**_ is based on Fast Http: a REST API-based open-source framework. It was built along the lines of Express.js with Fast HTTP.
- _**Fast HTTP**_ is better than the basic http/net package. Fast Http is better in performance because of worker pool versus memory allocation for different go routines in the http/net package. It is not compatible with HTTP/2 and the Beego web app framework. It can handle 100000 transactions per second and 1 million network connections.
- _**Martini**_ can also integrate with third-party services. well documented and adopted by many developers.

# Building scalable and responsive web app
Go language is popular for developing concurrent web apps. 
Go Lang provides a unit testing framework. 
Its applications easily handle Exceptions and errors. 
It has JSON handling features. 
Go applications are performant and scalable. 
For scalability, Go Lang-based microservices can be created and deployed on the cloud. Web pages developed in Go Lang web frameworks can be integrated with microservices.

Go Lang's inherent design principles and powerful features enable the creation of web applications that can handle significant traffic while maintaining high responsiveness and reliability.

Go's concise syntax promotes clean and maintainable code, reducing the cognitive load on developers and making collaboration more effective. Unlike languages that suffer from feature bloat, Go Lang's minimalist approach ensures that developers focus on building functional, efficient applications without unnecessary complexity. The language's comprehensive standard library includes packages tailored for web development, such as net/http for handling HTTP requests and html/template for generating dynamic HTML content.


Leveraging these built-in capabilities allows developers to
build robust web applications with minimal external
dependencies, fostering a more cohesive development
experience.


Concurrency is a key strength of Go Lang, setting it apart
from many other programming languages. Goroutines,
lightweight threads managed by the Go runtime, enable the
efficient handling of multiple concurrent tasks. This
capability is crucial for building responsive web applications
that need to manage numerous simultaneous user requests,
background tasks, and real-time updates. By utilizing Go's
concurrency model, developers can ensure their applications
remain performant even under heavy loads, providing a
seamless user experience. Channels facilitate
communication between goroutines, further enhancing the
efficiency and reliability of concurrent processes.


Modularity is another fundamental principle in developing
web applications with Go Lang. Organizing code into well-
defined, reusable packages promotes better code
management, scalability, and maintainability. Each package
should adhere to the Single Responsibility Principle
(SRP), ensuring that each module has a specific role within
the application. This modular approach simplifies testing and
debugging, as individual components can be developed and
tested in isolation. Dependency injection further decouples
components, enhancing flexibility and enabling more
effective unit testing. With a modular design, developers can
efficiently implement continuous integration and deployment
practices, streamlining the development pipeline and
facilitating easier updates and feature integration.


Security is paramount in web development, and Go Lang
provides several features to build secure applications.


Implementing robust authentication and authorization
mechanisms ensures that only authorized users can access
sensitive resources. Secure data transmission can be
achieved using TLS/SSL, protecting data from eavesdropping
and tampering. The standard library offers tools to mitigate
common security vulnerabilities, such as SQL injection and
cross-site scripting (XSS). By adhering to security best
practices, regularly updating dependencies, and conducting
security audits, developers can safeguard their applications
against potential threats and build trust with their users.


RESTful API design principles are integral to Go Lang-based
web app architecture. Designing APIs that follow REST
principles—such as using meaningful HTTP verbs, stateless
communication, and resource-based URLs—ensures that the
application remains intuitive and easy to use. Proper
documentation and versioning of APIs facilitate integration
with other systems and allow for the smooth evolution of the
application over time. By adhering to RESTful design
principles, developers can create interoperable and scalable
web applications that meet modern standards.


Go Lang's tooling ecosystem enhances the development
process, providing tools like go fmt for code formatting, go
vet for static analysis, and go test for unit testing to
maintain code quality and consistency. The Go module
system simplifies dependency management, making it easier
to manage and update external libraries. Leveraging these
tools ensures that the codebase remains clean, efficient, and
maintainable. Continuous integration and continuous
deployment (CI/CD) pipelines further enhance the
development workflow, enabling automated testing and
deployment for faster and more reliable releases.


Building scalable and responsive web applications with Go
Lang involves leveraging its simplicity, concurrency,
modularity, security, and adherence to RESTful design
principles. By following these principles and utilizing Go's
powerful features and tools, developers can create robust,
high-performance web applications that meet the demands
of modern users. Whether developing a small project or a
large-scale enterprise application, Go Lang's architecture
principles provide a comprehensive framework for achieving
excellence in web development. Its simplicity, combined with
powerful concurrency and an extensive standard library,
makes Go Lang an invaluable tool for building sophisticated
web applications that are both efficient and easy to
maintain. By embracing these principles, developers can
ensure long-term success and adaptability in the ever-
evolving landscape of web development.


Tools and techniques
Go routines can be created and scaled easily to handle
concurrency. To handle concurrent web requests, web
servers, and applications can create go routines to handle
concurrent jobs and parallelize the requests to give the
response. You can use containers like Docker or Kubernetes
to make deployment easy in cloud environments. Monitoring
and managing the web application can be done with tools
like Prometheus/Nagios/Grafana/Graphite and other
commercial tools like New Relic, Datadog, etc. Go lang
memory management and garbage collection are better
compared to other language-based tech stacks. Go Lang-
based tech stacks help meet the time to market by adding
new features and changing product features. Code Quality
can be checked using the GoLint Tool. Gofmt tool helps in
formatting the code for constants, strings, data inputs, and
variable values. Godoc can be run as a server that hosts the
documentation. Package management can be done in the Go
lang-based tech stack. There are code generators, code
analyzers, and open-source tools to automate the code
development. CI/CD tools like Gitlab CI and Jenkins can be
used for the deployment of the GoLang web application. Go
Test can be used to execute unit tests and gather testing
metrics. You can use GoVet to find defects in the code. Go
lang-based tech stack has features for data encryption and
decryption. The web application can be deployed with SSL.


Let us look at the deployment environments:

The artifacts, excluding code and configuration files that are not stored in the software configuration management tool or code repository, are as follows:
- Different environment-specific database settings
- Different environment-specific cache server settings
- Different environment-specific server and service credentials
- Different environment-specific API end-point URLs and payment gateway URLs
- Runtime and deployment time configuration parameters for different environments

## Go Lang web app challenges
Building web applications with Go Lang presents a unique set of challenges that developers must navigate to ensure successful project outcomes. While Go Lang offers simplicity, performance, and a robust standard library, these advantages come with their own hurdles that require careful
consideration and skillful management.

One of the primary challenges in developing Go Lang-based
web apps is its relatively steep learning curve for developers
new to the language. Go's minimalist syntax and unique
approach to error handling can be difficult to grasp for those
accustomed to more verbose languages. Additionally, Go
Lang's strict typing and lack of generics can make code
implementation more cumbersome, leading to increased
development time as developers adapt to its paradigms.


However, the trade-off is that once mastered, Go Lang's
simplicity and clarity can lead to more maintainable and
efficient codebases.


Concurrency, while a standout feature of Go Lang, also poses
significant challenges. Efficiently managing goroutines and
channels to handle concurrent tasks requires a deep
understanding of Go's concurrency model. Improper use of
these features can lead to issues such as race conditions,
deadlocks, and resource contention, which can be difficult to
debug and resolve. Ensuring thread safety and proper
synchronization in a concurrent environment demands
careful planning and testing, which can extend development
timelines.


Modularity in Go Lang is another area where developers may
encounter difficulties. While Go promotes a modular code
structure, organizing large codebases into well-defined
packages can be challenging. Ensuring that each package
adheres to the Single Responsibility Principle (SRP) and
managing dependencies effectively requires meticulous
design and foresight. Additionally, Go's approach to package
management and the absence of a formal package
versioning system in its early days have historically posed
challenges for developers, though the introduction of Go
modules has alleviated some of these issues.


Security concerns are paramount in web application
development, and Go Lang developers must navigate
various challenges to ensure robust security. Implementing
secure authentication and authorization mechanisms,
protecting data in transit with TLS/SSL, and mitigating
common vulnerabilities such as SQL injection and cross-site
scripting (XSS) requires a thorough understanding of
security best practices. Additionally, Go's relatively young
ecosystem means that developers may have to rely on
community-contributed libraries for certain security
functionalities, which can vary in quality and support.


API design is another critical challenge when building web
applications with Go Lang. Adhering to RESTful principles
and ensuring that APIs are intuitive, well-documented, and
versioned properly requires careful planning and execution.


Developers must balance the need for a clean and consistent
API design with the flexibility to adapt to changing
requirements and integrate with other systems. This can be
particularly challenging in large-scale applications where
multiple teams may be involved in API development.


The tooling ecosystem in Go Lang, while powerful, also
presents challenges. While tools, like go fmt, go vet, and go
test, are invaluable for maintaining code quality and
consistency, setting up and integrating these tools into a
streamlined development workflow can be complex.


Continuous integration and continuous deployment
(CI/CD) pipelines require careful configuration to ensure
automated testing and deployment processes run smoothly.


Developers must also stay up-to-date with the evolving
ecosystem and best practices to leverage these tools
effectively.


Building web applications with Go Lang involves navigating a
range of challenges, from mastering the language's unique
syntax and concurrency model to ensuring modularity,
security, and effective API design. While these challenges
can be daunting, they also present opportunities for
developers to deepen their skills and build robust, high-
performance web applications. By understanding and
addressing these challenges, developers can harness the full
potential of Go Lang to create scalable, maintainable, and
secure web applications that meet the demands of modern
users. Whether working on small projects or large-scale
enterprise applications, the journey of building Go Lang-
based web apps is both rewarding and enriching, providing
valuable insights and experiences along the way.


Now, let us look at design principles for designing web
applications.

# Web app design principles
Designing a web application is a difficult task, especially
when you want to build a product that has multiple features
and different types of users. The product to be built needs to
be a market fit in terms of competing with other products in
the same space.


The key design principle is to have loose coupling in the
application design. The side effects are expected when the
application is not loosely coupled. Any change in the existing
product feature or application scaling in terms of geography
or adding new products/categories/catalogs becomes tough
to implement. Once the product is developed and deployed,
it is not possible to change the design.


Creating web applications with Go Lang requires a thoughtful
approach to architecture, focusing on efficiency, scalability,
and maintainability. Go Lang's architecture principles revolve
around its simplicity, concurrency capabilities, and strong
type system, making it an excellent choice for developing
modern web applications.


One of the fundamental principles of Go Lang-based web app
architecture is its emphasis on simplicity and readability. Go
Lang's syntax is designed to be clean and straightforward,
reducing the cognitive load on developers. This simplicity
extends to the language's standard library, which provides
powerful tools for web development without the need for
additional dependencies. The net/http package, for
instance, offers a robust foundation for building web servers
and handling HTTP requests. By leveraging Go's built-in
capabilities, developers can create efficient and
maintainable web applications with minimal overhead.


Go Lang's web app architecture principles emphasize
simplicity, concurrency, modularity, security, and adherence
to RESTful design. By following these principles, developers
can create scalable, maintainable, and high-performance
web applications that meet the demands of modern users.


Go Lang's powerful features and robust tooling ecosystem
provide a solid foundation for building sophisticated web
applications, ensuring long-term success and adaptability.


Whether you are developing a small project or a large-scale
enterprise application, Go Lang's architecture principles offer
a comprehensive framework for achieving excellence in web
development:
Figure 16.4: SOLID principles
Let us revisit the SOLID principles covered in Chapter 9, Go
Dependency Injection and SOLID:
SOLID principles were created by Robert C. Martin. They are
design tenets for creating and developing software. These
tenets help in making the software extensible, scalable, and
enhance-able.

# The SOLID principles are as follows:
- Single Responsibility Principle (SRP): A class or module needs to have a single responsibility. This helps in making the software modular. New enhancements can be added to the software easily. Defects can be analyzed, and root causes can be detected easily.
- Open/Closed Principle (OCP): Software is designed and coded so that it is open for extending functionality.
The code is  closed for modifying the existing functionality. By adding to the code, new behaviors in the software can be done easily, and new features can be added to the software.
- Liskov Substitution Principle (LSP): What is wanted here is something like the following substitution property: If for each object obj1 of type S, there is an object obj2 of type T such that for all programs P defined in terms of T, the behavior of P is unchanged when obj1 is substituted for obj2 then S is a subtype of T.
- Interface Segregation Principle (ISP): Software Clients need not be relying on interfaces. Typically, interfaces are designed with minimal functionality. This helps in cutting down the dependencies between the modules.
- Dependency Inversion Principle (DIP): Top-layer software modules need not have to rely on bottom-layer modules. The top and bottom layers need to rely on software interfaces and abstractions. Detailed features are abstracted out by having abstract classes.

This helps in making the software modular and decoupled. The less decoupled the software, the more it helps in enhancing and testing the features quickly. 

The above SOLID principles are considered while designing the web application for a given functional requirement.

Before getting into system functional requirements, the common infrastructural components need to be designed, and their requirements need to be identified. They are as follows:
- Message and information logging
- System error/Exception handling
- External services and interface integration
- Data persistence
- System data access
- System data transfer
- Discovery/Naming and looking up of services
- Concurrent jobs
- System transaction management
- Product internationalization
- User authentication and authorization
To design the functional requirements of the system, the
microservices approach helps in decoupling the functional
modules from each other and identifying the services to be
grouped in a module. The goal is to avoid a monolithic
application that has all modules and services bundled in a
single package. Each microservice or module of services
needs to be capable of deploying and communicating with
other microservices.


During the requirements analysis, business entities, business
processes, and events are determined and grouped into a
logical functional module. A module will have an orchestral
service that can communicate with a microservice. You can
have a rules engine microservice that captures the functional
business rules. These rules will span business entities.


The web application will have a user interface that will be
based on a JavaScript/TypeScript framework like React,
Angular, Vue.js, and others. The key requirements which
need to be addressed in this layer are as follows:
User navigation
Process workflow
User input validation rules
Handling complex user interface interaction
Data validation framework
System default values
User friendly error messages mapped to system error
messages.


User personalization
User/App state management
User session tracking
Responsive design
Mobile/Desktop/Web page requirements
Now, let us look at the principles of web app architecture.


Web app architecture principles
Creating web applications with Go Lang requires a thoughtful
approach to architecture, focusing on efficiency, scalability,
and maintainability. Go Lang's architecture principles revolve
around its simplicity, concurrency capabilities, and strong
type system, making it an excellent choice for developing
modern web applications.


Go Lang's web app architecture principles emphasize
simplicity, concurrency, modularity, security, and adherence
to RESTful design. By following these principles, developers
can create scalable, maintainable, and high-performance
web applications that meet the demands of modern users.


Go Lang's powerful features and robust tooling ecosystem
provide a solid foundation for building sophisticated web
applications, ensuring long-term success and adaptability.


Whether you are developing a small project or a large-scale
enterprise application, Go Lang's architecture principles offer
a comprehensive framework for achieving excellence in web
development.


The focus of the web app architecture principles is on the
following:
Code/component reusability
System portability
Web application scalability
Web app usability
Component extensibility
While architecting the web application, you need to gather
the system sizing requirements. They are as follows:
Number of concurrent users
Transactions per second (request rate)
Total number of users in the system
Maximum/minimum data size for transfer
Response time for the web page
After gathering the system sizing requirements, the
hardware required on the cloud/on-premises is decided for
the web server, database, and storage repository. The other
candidates for the system sizing are:
App server
Middleware server
LDAP/AD server
The system deployment specifications are determined now,
and the external services to be integrated need to be
identified. From the security and performance point of view,
network communication protocol, network bandwidth,
militarized zones, demilitarized zones, firewall, intrusion
detection system, API gateways, and Serverless components
are identified in the system deployment specification.


First, let us look at how to create a Go web application
project. The project directory will have the following
packages:
cmd
pkg
internal
vendor
api
web
configs
init
scripts
build
deployment
test
The cmd package will have the entry point of the web app
main.go pkg package has the external services and
components. The internal package will have the
dependencies and the internal code for the developed
services. The vendor package will have the web app
dependencies. The api package has the REST services,
swagger spec, schema, and interfaces. The web package has
the web pages, content, and style sheets. The Configs
package has the configuration files for the external and
internal services. The Init package has the
start/stop/bootstrapping environment and the configurations.


The scripts package has the build/install/analysis/operations-
related scripts. The build package has the scripts and the
configurations for creating a build. The deployment package
has the environment and the configuration details. The test
package has the unit tests and the functional tests.


Now, let us look at the different architecture methodologies.


First, let us look at the clean architecture methodology.


Clean architecture is an architectural methodology where the
focus is on having an independent presentation layer and
frameworks that are independent of business rules, services,
and middleware layer. Robert C. Martin created the clean
architecture methodology. This methodology helps in
creating an architecture that is independent of the database
that is used. The system built needs to be unit tests, and a
unit test framework needs to be created for the functional
and infrastructural services and components.


Figure 16.5: Clean architecture, four circles
In Figure 16.5, the colors signify the enterprise-level and
app-level business rules. Let us look at the specifications:
Figure 16.6: Clean architecture, four circles color schemes
Clean architecture can be visualized as four concentric
circles. The outer circle has the web, user interface (UI),
external interfaces, database, and devices like mobile and
tablets. The second circle has the controllers, gateways, and
presenters. The third circle has the use cases, and the center
circle has entities.


From the user interface, the user invokes the steps in a use case. 
Then, the interactor of the use case sends the input to the controller. 
The controller processes the request input and sends the response, which is shared with the user from the output port of the use case. The key part of the clean architecture is the entities. They represent the business logic and rules. They are minimally impacted by external requests or API calls. They are independent of the other circles.

The entities are tied up with input validation, and the error messages are to be displayed to the user. Use cases rely on entities, and any change in the entities comes from the use case. Let us take a simple example: The user as an entity.

Use cases like registration, login, logout, and others impact the user entity, which has the attributes like username, password, email, and others.

The interface adapters layer has data converters that transform the data from one format to another. The format at the external interface might be different from the one used in web applications. There will be API, services, request handlers, response formats, input validators, and middleware. The database can be relational or No-SQL, based on the structure of the data. The data used can be structured, semi-structured, and unstructured data.

Unstructured data needs to be stored in a No-SQL database.

# Hexagonal architecture 
helps in identifying the concerns and dependencies in the web application. It also helps in
decoupling the concerns by having multiple layers. Each layer will have a responsibility. There will be boundaries at the intersection of the layers. Ports are like interfaces that get data from external interfaces. Data can come from external interfaces and be stored in a data source. Adapters implement the interfaces (ports) by creating an implementation. They help in transforming data and processing the data requests to give a response from the data source. Domain is the core of hexagonal architecture, which includes business logic and rules. There are driving and driven sides, which are the input and output of the web application. Let us look at the hexagonal architecture, which is layered:

Let us look at an e-commerce application that allows customers, orders, and purchases to be made online through a web and mobile application.

Let us first look at the project structure of the e-commerce web application:

    cmd/
        main.go
    internal/
        application/
            customer.go
            purchase.go
        domain/
            customer.go
            purchase.go
        ports/
            customer_port.go
            purchase_port.go
    web/
        handlers/
            customer_handler.go
            purchase_handler.go

cmd package: This package has the main.go. It is the entry point that integrates and injects the dependencies.

- internal/application package: This package has customer and purchase use cases and related entities in the domain package
- internal/domain package: This package has the entities that have the business rules and the logic.
- internal/ports package: This package has customer management and purchase handling services.

- web/handlers package: This package has the request handlers for requests related to customer and purchase use cases.

Use cases can be registration, login, and logout for the customer. 
Purchase-related use cases can be order creation, product selection, adding an address to the order, and paying for the order. Order status is a use case related to the customer.

Let us look at the code for customer_port.go as follows:
```go

package ports

import "context"
import "web-app/internal/domain"

type CustomerRepository interface {
	SaveCustomer(ctx context.Context, customer *domain.Customer) error
	GetCustomerByID(ctx context.Context, customerID string) (*domain.Customer, error)
}
type CustomerUseCase interface {
	RegisterCustomer(ctx context.Context, name, email string) error
	GetCustomerByID(ctx context.Context, customerID string) (*domain.Customer, error)
}
```

Now, let us look at one of the adapters, customer.go:
```go

package application

import "context"
import "webapp/internal/domain"
import "webapp/internal/ports"

type CustomerUseCase struct {
	customerRepository ports.CustomerRepository
}

func NewCustomerUseCase(customerRepo ports.CustomerRepository) *CustomerUseCase {
	return &CustomerUseCase{customerRepository: customerRepo}
}
func (customerUC *CustomerUseCase) RegisterCustomer(ctx context.Context, name, email string) error {
}
func (customerUC *CustomerUseCase) GetCustomerByID(ctx context.Context, customerID string) (*domain.Customer, error) {
}
```
Now, let us look at how the dependencies are injected in the entrypoint. The server is started in main.go:
```go

package main

import (
	"context"
	"net/http"
	"your-app/internal/application"
	"your-app/internal/ports"
	"your-app/web/handlers"
)

func main() {
	customerRepo := initialize_customerRepo()
	customerUseCase := application.NewCustomerUseCase(customerRepo)

	http.HandleFunc("/customers/register", handlers.NewCustomerHandler(customerUseCase).RegisterCustomer)

	http.HandleFunc("/customers/{customerID}", handlers.NewCustomerHandler(customerUseCase).GetCustomerByID)

	start_http_web_server()
}
```
Now, let us look at the web request handlers—
customer_handler.go:
```go

package handlers

import (
	"net/http"
	"webapp/internal/application"
)

type CustomerHandler struct {
	customerUseCase application.CustomerUseCase
}

func NewCustomerHandler(customerUseCase application.CustomerUseCase) *CustomerHandler {
	return &CustomerHandler{customerUseCase: customerUseCase}
}

func (handler *CustomerHandler) RegisterCustomer(writer http.ResponseWriter, request *http.Request) {
}

func (handler *CustomerHandler) GetCustomerByID(writer http.ResponseWriter, request *http.Request) {
}
```

## Hexagonal architecture has the following advantages:
- Easily testable
- Scalable
- Change tolerance
Unit testing can be done easily as the business rules and logic are separated from the external services. The system can be maintained and functional updates can be done without impacting the other components. The system consists of different modules, and scaling can be done by deploying different components in a distributed network. As in any architecture methodology, trade-offs need to be made between complexity and performance.





