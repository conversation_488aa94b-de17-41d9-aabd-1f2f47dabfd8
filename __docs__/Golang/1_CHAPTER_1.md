# 1 CHAPTER 1 Go programming 



Basic programming in Go 

    Data types
    Variables
    Constants
    Operators
    Conditional statements
    Strings and arrays
    Maps
    Pointers and structures
    Recover, defer, and panic
    

```go
package main

import "fmt"

func main() {
	fmt.Println("My first program in Go Lang")
}
```

```shell

go run first_program.go
```

You can check the tools used by the Go Lang developers at: https://pkg.go.dev/golang.org/x/tools. These tools help

in formatting the code, auto-completion while developing, documentation of the code, debugging, refactoring, securing the IP, and gathering metrics coverage related to unit testing.

Java and Python have similar equivalents in their toolkit. Java Doc, Sonar Cube, Checkstyle, Junit5, Mockito, PMD, SpotBugs, and others are equivalent tools in the Java toolkit.

# Data types
In Go language, data types are categorized as below: 
- Basic types
- Composite types
- Reference types
- Interface types

## Basic types
numbers, strings, and Boolean types:
- number data type can be divided into three different data types: Integers, Floating point, Complex
- String type has Unicode characters in sequence.
- Boolean types are true or false types.

```go

package main

import "fmt"

type numVar int
type strVar string
type boolVar bool

func main() {
	var intVar numVar = 98
	var phraseVar strVar = "checking string"
	var falseVar boolVar = false
	fmt.Printf("%v\n", intVar)
	fmt.Printf("%T\n", intVar)
	fmt.Printf("%v\n", phraseVar)
	fmt.Printf("%T\n", phraseVar)
	fmt.Printf("%v\n", falseVar)
	fmt.Printf("%T\n", falseVar)
}
```

```shell

go run data_types.go
```

    98    main.numVar
    checking string    main.strVar
    false    main.boolVar

verbose (%v) and type (%T) formats.


Note: Go language is statically typed. 
Static Type variable is defined only once and the data is stored in the variable of a specific type. 


## Composite types
Arrays and structs are composite types. 
- Arrays consist of the same typed variables, 
- structs have different typed variables. 

```go

package main

import "fmt"

func main() {
	var arr [4]string
	arr[0] = "My"
	arr[1] = "Name"
	arr[2] = "Is"
	arr[3] = "Tom"
	fmt.Println(arr[0], arr[1], arr[2])
	fmt.Println(arr[3])
	
	fibonacci_numbers := [10]int{0, 1, 1, 2, 3, 5, 8, 13, 21, 34} //marker1
	fmt.Println(fibonacci_numbers)
}
```

In the above example, an array of string type and integer types are declared and initialized. 

### Slices 
can expand in size or pick a subset of elements in an array:
```go

package main

import "fmt"

func main() {
	fibonacci_numbers := [10]int{0, 1, 1, 2, 3, 5, 8, 13, 21, 34}
	fmt.Println(fibonacci_numbers)
	
	var subset_fib []int
	subset_fib = fibonacci_numbers[2:6]
	fmt.Println(subset_fib)
}
```

An array has a specified size. 
A slice can be dynamic and handle different types. 
A slice is represented as []T, where T is a type.

## Reference types
In the Go language, there are container types that can be used to have data groups or sets. The first-class citizens are slice, array, and map. 

Pointers and channels are second-class citizens. 

Containers hold the values of the elements which belong to the same types. In a map, the element will have a key and a value. Maps need less memory compared to slices and arrays. The array, slice, and map are represented as `[X]Y`, `[]Y`, and `map[Z]Y` notations, respectively. 
In the notation, X is a non-negative integer, which is the count of the elements in the container, and Y is the arbitrary type, which is the type of the element in the container. Z is another arbitrary type, which is the type of the key in the map. 

A channel is used as a container of element values for the execution of the go routines. 

Pointers represent the memory address of the element value.

Container types like slice, map, channel, pointers, and function types are reference types. Reference type stands for a value type, which is a pointer. Map and channel types use pointers to represent the internal structure. Slice is a composite type—Struct. It has length and capacity, which are integer types and pointers to the elements. Functions and methods are pointers that stand for functions and methods.

Now, let us look at slices and how references can be used for slices:
```go

package main

import (
	"fmt"
	"reflect"
)

func changeSlice(slice []int) {
	reflect.ValueOf(&slice).Elem().SetLen(5)
	reflect.ValueOf(&slice).Elem().SetCap(5)
	fmt.Println("slice3>", len(slice), cap(slice))
}
func main() {
	slice := make([]int, 2, 8)
	fmt.Println("slice1>", len(slice), cap(slice))
	changeSlice(slice)
	fmt.Println("slice2>", len(slice), cap(slice))
}

```

```shell

go run reference_types.go
```

The output shows how the references help in referencing other values when passed as a pointer. This applies to map, pointer, channel, and interface types.

## Interface types
In the Go language, the interface is an abstract type that
consists of method signatures. You cannot have instances of
the interface. You can create an interface with the type
(keyword) interface name interface (keyword). More than
one interface can be implemented by a data type.


Now, let us look at an example of interfaces:
package main
import "fmt"
type vehicle interface {
getSpeed() float64
getDistanceTravelled() float64
}
type car struct {
brand              string
manufacturing_year int
}
func (Car car) getSpeed() float64 {
return 120
}
func (Car car) getDistanceTravelled() float64 {
return 4000
}
func main() {
var veh vehicle
veh = car{"Toyota Tercel", 1997}
fmt.Println("Speed of the Vehicle :",
veh.getSpeed())
fmt.Println("Distance Travelled by the
Vehicle:", veh.getDistanceTravelled())
}
First, you need to save the above code in
instance_type.go. You can now compile and run the
instance_type.go. The command is shown as follows:
Figure 1.7: Interface_Type example execution output
The output shows how the interface abstracts out the
method implementation. All methods need to be
implemented by the struct. Vehicle is an interface, and the
car is a structure. When a car is instantiated, it returns a
vehicle.


Variables
In the Go language, variables can be instantiated with an
initializer. An initializer can be an expression that is
evaluated at execution time. It can be declared in a method,
for loop, or a class. The variable scope can be local if it is
declared inside a method or a code block. The scope will be
global when it is declared outside a method or a code block.


Now let us look at an example of interfaces:
package main
import "fmt"
func main() {
var var1 int = 55
var var2 float32 = 4.5
fmt.Printf("var1 value is : %d\n",
var1)
fmt.Printf("var2 value is : %f\n",
var2)
}
First, you need to save the above code in a variable.go file.


You can now compile and run the variable.go. The
command is shown as follows:
Figure 1.8: Variable example execution output
The output shows how the variables of different types, like
integer and float, are declared and instantiated.


Constants
In the Go language, constants are defined and initialized
once. The value cannot be changed after that. Constant
types can be integer, float, character, and string type.


Now, let us look at an example for interfaces:
package main
import "fmt"
const JVAL = 5.87
func main() {
const PRO = "Bike has wheels"
fmt.Println("Cycle", PRO)
fmt.Println("JValue is", JVAL)
const RIGHT = true
fmt.Println("Checking Boolean", RIGHT)
}
First, you need to save the above code in constant.go file.


You can now compile and run the constant.go. The
command is as follows:
go run constant.go
The output will be as follows:
Figure 1.9: Constant example execution output
The output shows how constants are declared for different
types like float, string, and Boolean.


Operators
In Go language, operators are arithmetic, relational, logical,
bitwise, assignment, and miscellaneous:
Arithmetic
operators
are
related
to
addition,
subtraction, multiplication, division, and Modulus
mathematical operations.


Relational operators are equal to, not equal to, greater
than, less than, greater than equal to, and less than
equal to comparisons.


Logical operators are AND, OR, and NOT operations.


Bitwise operators are bitwise AND, bitwise OR, bitwise
XOR, left shift, right shift, and AND NOT operators.


Assignment operators are simple assignment, add
assignment, subtract assignment, multiply assignment,
division assignment, modulus assignment, Bitwise AND
assignment, Bitwise Exclusive OR, Bitwise inclusive
OR, Left shift AND assignment operator, and Right
shift AND assignment.


Now, let us look at an example for each of the above
operators.


package main
import "fmt"
func main() {
var1 := 46
var2 := 87
//Arithmetic Operators
// Addition Operator
output1 := var1 + var2
fmt.Printf("Result of var1 + var2 = %d",
output1)
// Subtraction Operator
output2 := var1 - var2
fmt.Printf("\nResult of var1 - var2 = %d",
output2)
// Multiplication Operator
output3 := var1 * var2
fmt.Printf("\nResult of var1 * var2 = %d",
output3)
// Division Operator
output4 := var1 / var2
fmt.Printf("\nResult of var1 / var2 = %d",
output4)
// Modulus Operator
output5 := var1 % var2
fmt.Printf("\nResult of var1 %% var2 = %d",
output5)
//Relational Operators
// ‘=='(Equal To Operator)
output11 := var1 == var2
fmt.Println(output11)
// ‘!='(Not Equal To Operator)
output12 := var1 != var2
fmt.Println(output12)
// ‘<‘(Less Than Operator)
output13 := var1 < var2
fmt.Println(output13)
// ‘>'(Greater Than Operator)
output14 := var1 > var2
fmt.Println(output14)
// ‘>='(Greater Than Equal To Operator)
output15 := var1 >= var2
fmt.Println(output15)
// ‘<='(Less Than Equal To Operator)
output16 := var1 <= var2
fmt.Println(output16)
//Logical Operators
if var1 != var2 && var1 <= var2 {
fmt.Println("True")
}
if var1 != var2 || var1 <= var2 {
fmt.Println("True")
}
if !(var1 == var2) {
fmt.Println("True")
}
//Bitwise Operators
// & (bitwise AND Operator)
output21 := var1 & var2
fmt.Printf("Result of var1 & var2 = %d",
output21)
// | (bitwise OR Operator)
output22 := var1 | var2
fmt.Printf("\nResult of var1 | var2 = %d",
output22)
// ^ (bitwise XOR Operator)
output23 := var1 ^ var2
fmt.Printf("\nResult of var1 ^ var2 = %d",
output23)
// << (left shift Operator)
output24 := var1 << 1
fmt.Printf("\nResult of var1 << 1 = %d",
output24)
// >> (right shift Operator)
output25 := var1 >> 1
fmt.Printf("\nResult of var1 >> 1 = %d",
output25)
// &^ (AND NOT Operator)
output26 := var1 &^ var2
fmt.Printf("\nResult of var1 &^ var2 = %d",
output26)
//Assignment Operators
// “="(Simple Assignment Operator)
var1 = var2
fmt.Println(var1)
// “+="(Add Assignment Operator)
var1 += var2
fmt.Println(var1)
//“-="(Subtract Assignment Operator)
var1 -= var2
fmt.Println(var1)
// “*="(Multiply Assignment Operator)
var1 *= var2
fmt.Println(var1)
// “/="(Division Assignment Operator)
var1 /= var2
fmt.Println(var1)
// “%="(Modulus Assignment Operator)
var1 %= var2
fmt.Println(var1)
}
First, you need to save the above code in operators.go file.


You can now compile and run the operators.go. The
command is as follows:
go run operators.go
The output will be as follows:
Figure 1.10: Operators example execution output
The output shows how operators are used in Go Lang related
to arithmetic, relational, assignment, bitwise, and bitwise
assignment operations.


Conditional statements
In Go language, control flow is managed by conditional
statements like If, If..else, If…else if…else, and switch…case
statements. Let us look at the If statement first.


if condition statement
if statement
The if statement has a code section invoked based on a
condition. The syntax is if condition { }.


Now, let us look at an example of an if statement:
package main
import (
"fmt"
)
func main() {
var str = "Adobe"
boolA := true
if boolA {
fmt.Println(str)
}
}
First, you need to save the above code in if_statement.go.


You can now compile and run the if_statement.go. The
command is as follows:
go run if_statement.go
The output will be as follows:
(base) apples-MacBook-Air:code bhagvan.kommadi$ go
run if_example.go
Adobe
(base) apples-MacBook-Air:code bhagvan.kommadi$
The output shows the code section, which prints Adobe if the
condition is that boolA is true.


if .. else statement
The if… else statement has code sections that are invoked
based on true and false conditions. The syntax is if
condition { } else {}.


Now, let us look at an example for the if..else statement.


package main
import (
"fmt"
)
func main() {
var str = "Adobe"
var defaultStr = "Google"
boolA := false
if boolA {
fmt.Println(str)
} else {
fmt.Println(defaultStr)
}
}
First, you need to save the above code in
if_else_statement.go file. You can now compile and run
the if_else_statement.go. The command is shown as
follows:
go run if_else_statement.go
The output will be as follows:
Figure 1.11: If…Else example, execution output
The output shows the code section, which prints Google if
the condition is that boolA is false.


if .. else if ..else statement
If… else if .. else statement has multiple code sections which
are invoked based on true conditions and one for false
conditions. The syntax is if condition { } else if {}
else if {}…else {}.


Now let us look at an example for if..else if..else statement:
package main
import (
"fmt"
)
func main() {
var str = "Adobe"
var str1 = "Twitter"
var str2 = "Amazon"
var defaultStr = "Google"
boolA := false
boolB := true
boolC := true
if boolA {
fmt.Println(str)
} else if boolB {
fmt.Println(str1)
} else if boolC {
fmt.Println(str2)
} else {
fmt.Println(defaultStr)
}
}
First, you need to save the above code in
if_else_if_elese_example.go. You can now compile and
run the if_else_if_else_example.go. The command is
shown as follows:
go run if_else_example.go
The output will be as follows:
Figure 1.12: Variable example execution output
The output shows how the code section which prints Twitter
as the first if else condition code section gets invoked.


Switch statement
The switch statement has multiple code sections in which
one of them is invoked. The syntax is switch variable {
case values: … case:value… default: }.


Now, let us look at an example of a switch statement:
package main
import (
"fmt"
"time"
)
func main() {
timeNow := time.Now()
var day int = timeNow.Day()
switch day {
case 6, 11, 17:
fmt.Println("Buy Groceries")
case 5, 16, 27:
fmt.Println("Play Golf")
case 3:
fmt.Println("Go to Church")
default:
fmt.Println("Read a magazine")
}
}
First, you need to save the above code in the
switch_example.go file. You can now compile and run the
switch_example.go. The command is shown as follows:
go run switch_example.go
The output will be as follows:
Figure 1.13: Switch example execution output
The output shows how the code section that prints the Buy
Groceries since the day the code was executed is 6th.


Strings and arrays
In this section, we will look at strings and arrays in the Go
language. Let us first look at strings.


Strings
In the Go language, the string is a set of different-width
characters. A string has characters that represent a single or
multiple bytes. Strings are encoded by UTF-8 type. The string
is a slice of bytes that can be read by the user. A string is
defined using double quotes “".


Now let us look at an example for strings:
package main
import "fmt"
func main() {
hello_string := "Hello World"
var string_var string
string_var = "initialized later"
fmt.Println("intiallizedString: ",
hello_string)
fmt.Println("declared and initialized
later: ", string_var)
var strings [5]string
strings[0] = "George"
strings[1] = "John"
strings[2] = "Tom"
strings[3] = "Vivan"
strings[4] = "Marlin"
fmt.Println("array of strings: ", strings)
}
First, you need to save the above code in the
strings_example.go file. You can now compile and run the
strings_example.go. The command is shown as follows:
go run strings_example.go
The output will be as follows:
Figure 1.14: Strings example execution output
The output shows how strings are initialized, declared, and
the creation of the string arrays.


Arrays
We discussed array types early in the chapter. Now, let us
take a deep dive. In the Go language, the array is a set
consisting of similar elements that can be stored in the
memory. Slices have the flexibility of having the capability of
expanding. Arrays start from index 0, and the last element’s
index will be n-1, where n is the number of elements.


Now, let us look at an example of arrays:
package main
import "fmt"
func main() {
string_array := [5]string{"Tom", "George",
"John", "Jim", "Bill"}
fmt.Println("Elements of string array:")
for i := 0; i < 4; i++ {
fmt.Println(string_array[i])
}
}
First, you need to save the above code in the
array_example.go file. You can now compile and run the
array_example.go. The command is shown as follows:
go run array_example.go
The output will be as follows:
Figure 1.15: Array example execution output
The output shows how arrays are created, initialized, and the
accessing of elements.


Maps
In the Go language, the map is a data structure that has keys
and values. It consists of pairs of keys and values. Maps help
in retrieving the values based on the keys quickly. Map
provides operations for getting, updating, and deleting the
values.


Now let us look at an example for maps:
package main
import "fmt"
func main() {
var integer_keymap map[int]int
if integer_keymap == nil {
fmt.Println("It is not
initialized")
} else {
fmt.Println("It is initialized")
}
string_keymap := map[int]string{
9:  "Bat",
23: "Ball",
14: "Cap",
19: "Pad",
4:  "Shoe",
}
fmt.Println("String keymap: ",
string_keymap)
}
First, you need to save the above code in the
maps_example.go file. You can now compile and run the
maps_example.go. The command is shown as follows:
go run maps_example.go
The output will be as follows:
Figure 1.16: Maps example execution output
The output shows how maps are declared and initialized with
values having integer and string keys.


# Pointers and structures

## Pointers
A pointer represents a variable, which stands for the memory address of a variable. The memory address is persisted using variables in the Go language. The format of a memory address typically starts with 0x. An example of a memory
address is 0xCCAAF. Pointers are also used to get access to the persisted data by using the memory address. Variables have names and are easy to access and persist data.

Now, let us look at an example of pointers:
```go

package main

import "fmt"

func main() {
	hexa_var1 := 0xCC
	hexa_var2 := 0x8F
	fmt.Printf("variable type of hexa_var1 is %T\n", hexa_var1)
	fmt.Printf("hexa_var1's value in hexadecimal is %X\n", hexa_var1)
	fmt.Printf("hexa_var1's value in decimal is %v\n", hexa_var1)
	fmt.Printf("variable type of hexa_var2 is %T\n", hexa_var2)
	fmt.Printf("hexa_var2's value in hexadecimal is %X\n", hexa_var2)
	fmt.Printf("hexa_var2's value in decimal is %v\n", hexa_var2)
}
```
`go run pointers_example.go`

## Structures
A structure in the Go language has a set of properties which
are of different types. This user-defined type is similar to the
class in the OOP paradigm. In the Go language, inheritance is
not possible with structure. The composition can be modeled
using structure.


We can have a structure defined for the customer. It can be a
user-defined type, as follows:
type Customer struct {
Name    string
ID      string
city    string
Pincode int
}
Now, let us look at an example of customer struct:
package main
import "fmt"
type Customer struct {
Name    string
ID      string
city    string
Pincode int
}
func main() {
var customer Customer
fmt.Println(customer)
customer1 := Customer{"Andy Griffith",
"CUST2341", "Atlanta", 30033}
fmt.Println("Customer1: ", customer1)
customer2 := Customer{Name: "Bill Smith",
ID: "CUST6532", city: "Boston", Pincode: 102108}
fmt.Println("Customer2: ", customer2)
customer3 := Customer{Name: "Thomas Hardy"}
fmt.Println("customer3: ", customer3)
}
First, you need to save the above code in the
structures_example.go file. You can now compile and run
the structures_example.go. The command is shown as
follows:
go run structures_example.go
The output will be as follows:
Figure 1.18: Structures example execution output
The output shows how structures are defined with initializers
and specifying the fields and values.


# Recover, defer, and panic
Recover/Defer/Panic is similar to the Try/Catch block in other languages. In the Go language, Recover /Defer/Panic helps in handling errors. When a Go routine throws an error, panic and Recover can help in managing the control of the program.

Now let us look at an example of Recover/Defer/Panic:
```go

package main

import "fmt"

func callPanic() {
	if a := recover(); a != nil {
		fmt.Println("RECOVER", a)
	}
}

func enterInput(lady *string, resort *string) {
	callPanic() // #marker2
	defer callPanic()
	if lady == nil {
		panic("Error: Lady name cannot be nil")
	}
	if resort == nil {
		panic("Error: Resort name cannot be	nil")
	}
	fmt.Printf("Lady Name: %s \n Resort: %s\n", *lady, *resort)
	fmt.Printf("enterInput completed")
}
func main() {
	Name_lady := "Annie"
	enterInput(&Name_lady, nil)
	fmt.Printf("main function completed")
}
```

`go run recover_example.go`

The output shows how panic and recovery help in handling errors in the Go routine. The recovery function helps prevent errors from panicking. 
It works in the same Go routine where the error is panicked. The recover function is always called the `defer` function.




Recover/Defer/Panic are used to handle errors that can occur at runtime or application-specific contexts.

