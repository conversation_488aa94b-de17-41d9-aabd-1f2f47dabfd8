# 6 CHAPTER 6

RPC Apps with gRPC
Introduction
In this chapter, the readers will learn how to build RPC apps using
Go. These apps are built using Google gRPC. Readers will know
about Protocol Buffers. gRPC clients will be developed to talk to
the gRPC server. Unit tests will be written to test the clients and
server code:
Figure 6.1: Go RPC Apps – Different End Points
Structure
This chapter covers the following topics:
RPC apps in Go Lang
Protocol buffers
gRPC clients
gRPC server
Objectives
By the end of this chapter, you can build command line and REST
API-based apps using gRPC with the help of examples.


RPC apps in Go Lang
In the Go language, gRPC is a popular Google framework for
processing remote procedure calls. It can help in connecting
distributed services and has been adopted by Netflix, IBM, Cisco,
and others. These are some features of gRPC:
This framework has the capability to handle load balancing,
health
monitoring,
authentication
and
tracing
using
plugins.


You can create a gRPC service using protocol buffers and a
binary serialization library. gRPC provides tools to generate
clients and server stubs for different technology stacks.


It can provide authentication using HTTP/2-based transport
plugins. HTTP/2 brings significant enhancements to web
communication by allowing multiplexing and improving
resource utilization. Multiplexing lets multiple requests and
responses be sent simultaneously over a single connection,
avoiding the head-of-line blocking issue in HTTP/1.1. This
results in faster page load times and more efficient use of
network resources. Additionally, HTTP/2 supports header
compression, reducing overhead and making data transfer
more efficient. These features collectively enhance the
browsing experience by making interactions smoother and
more responsive, leading to better overall performance.


Many products are built using gRPC as its messages are 1.5
times smaller in size than JSON messages. Reduced
bandwidth and fast processing speed is the reason gRPC is
popular.


gRPC supports Java, Python, Ruby, and Go Technology
stacks.


Processing speeds come down with gRPC API compared to
JSON API for messaging and communication.


You can access the gRPC repo at
https://github.com/grpc/grpc-go. Let us look at the
helloworld server code in the examples/helloworld folder.


Have a look at the server code:
main.go
package main
import (
"context" "flag" "fmt" "log" “net"
"google.golang.org/grpc"hgrpc "hellogrpc/hellogrpc"
)
var (
port = flag.Int("port", 50051, "The server
port")
)
type server struct {
hgrpc.UnimplementedGreeterServer
}
func (s *server) SayHello(ctx context.Context, in
*hgrpc.HelloRequest) (*hgrpc.HelloReply, error) {
log.Printf("Received: %v", in.GetName())
return &hgrpc.HelloReply{Message: "Hello " +
in.GetName()}, nil
}
func main() {
flag.Parse()
lis, err :=
net.Listen("tcp", fmt.Sprintf(":%d", *port))
if err != nil {
log.Fatalf("failed to listen: %v", err)}
s := grpc.NewServer()
hgrpc.RegisterGreeterServer(s, &server{})
log.Printf("server listening at %v", lis.Addr())
if err := s.Serve(lis); err != nil
log.Fatalf("failed to serve: %v", err)
}}
Now, let us look at the client implementation:
main.go
package main
import ( "context" "flag" "log" “time"
"google.golang.org/grpc"
"google.golang.org/grpc/credentials/insecure" pb
"helloworld/helloworld"
)
const ( defaultName = "world"
)
var (
addr = flag.String("addr", "localhost:50051",
"the address to connect to"
name =
flag.String("name", defaultName, "Name to greet")
)
func main() {
flag.Parse()
conn, err :=
grpc.Dial(*addr,
grpc.WithTransportCredentials(insecure.NewCredentials(
)))
if err != nil {
log.Fatalf("did not connect: %v", err)
}
defer conn.Close()
c := hgrpc.NewGreeterClient(conn)
ctx, cancel :=
context.WithTimeout(context.Background(), time.Second)
defer cancel()
r, err := c.SayHello(ctx,
&hgrpc.HelloRequest{Name: *name})
if err != nil {
log.Fatalf("could not greet: %v", err)
}
log.Printf("Greeting: %s", r.GetMessage())
}
The helloworld proto code is in the helloworld folder. We will
go through protocol buffers. in the next chapter. To compile and
run the above Go language program, ensure that you have the
latest Golang executable.


1. You can download it from https://golang.org/dl/. Based on
the operating system you use, you can download the
appropriate executable.


2. You can verify the installation by running the following
command:
go version
3. After verifying the installation of the Go compiler, you can
compile and run the using the below commands. The
commands are shown as follows:
go mod init helloworld
go mod tidy
4. The output will be as shown in the following figure from the
server side:
Figure 6.2: helloworld program output
5. Let us look at the client side and how the gRPC calls are
made. This is the output:
(base) apples-MacBook-Air:helloworld
bhagvan.kommadi$ go run greetgRPC_client/main.go
2023/05/14 18:03:26 Greeting: Hello world
Note: Remote procedure calls-based software was very popular in the
80s. IDL and CORBA successfully handled RPC messages across the
distributed nodes in a wide area network. gRPC is different from RPC
in architectural style and function calls. Started by Google in 2015, it
has become popular in the last 5 years.


Protocol buffers
In this section, we will look at Protobuf, equivalent to IDL for
gRPC. Protobuf consists of function contracts and is a protofile
where data is persisted. Implementing gRPC with Protobuf
involves several steps:
1. First, define your service in a .proto file detailing the
methods and messages that will be used.


2. Compile the .proto file using the Protocol Buffers compiler
(protoc), which will generate client and server code in your
chosen programming language.


3. Implement the server by writing the service logic to handle
incoming requests. After that, create the client to send
requests to the server.


4. Establish a connection between the client and server and
test to ensure communication is working as expected. This
setup provides efficient and scalable communication
between services.


Let us do this in code. As a first step, a service can be defined in
.proto file. Then for the second step, server and client code can
be created by using the protobuf compilation tool. gRPC API can
be used to create a go gRPC client and server.


We are going to build gRPC based server and clients to handle
product catalog and updates. Products will be initialized by the
server into the catalog saved products. Add Product & Search
Product requests will be sent using productspec messages. The
product will have attributes name, image, and category name.


catalog.proto
syntax = "proto3";
option go_package = "gobook/grpc/catalog";
option java_multiple_files = true;
option java_package = "go.book.catalog";
option java_outer_classname = "CatalogProto";
package catalog;
service Catalog {
rpc GetProduct(ProductSpec) returns (Product) {}
rpc ProductList(Category) returns (stream Product)
{}
rpc RecordProduct(stream ProductSpec) returns
(Product) {}
rpc ProductChat(stream ProductSpecNote) returns
(stream ProductSpecNote) {}
}
message Product {
string name = 1;
string image = 2;
}
message Category {
string name = 1;
}
message ProductSpec {
string name = 1;
}
message ProductSpecNote {
Category cat = 1;
ProductSpec spec = 2;
}
In the service, proto messages are defined with their properties
and field order numbers.


Now, let us look at the commands to generate protobuffers code
using protoc-gen-go. The output will be as shown in the
following figure:
Figure 6.3: Install command output
The protoc tool generates the serializers for Product and other
message types in the catalog as follows:
catalog.pb.go
// Code Snippet generated by protoc-gen-go. DO NOT
EDIT.


type Product struct {
state         protoimpl.MessageState
sizeCache
protoimpl.SizeCache
unknownFields
protoimpl.UnknownFields
Name    string
`protobuf:"bytes,1,opt,name=name,proto3"
json:"name,omitempty"`
Image   string
`protobuf:"bytes,2,opt,name=image,proto3"
json:"image,omitempty"` Catname string
`protobuf:"bytes,3,opt,name=catname,proto3"
json:"catname,omitempty"`
}
func (x *Product) Reset() {
*x = Product{}
if
protoimpl.UnsafeEnabled {
mi := &file_catalog_catalog_proto_msgTypes[0]
ms :=
protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
ms.StoreMessageInfo(mi)}}
func (x *Product) String() string {
return protoimpl.X.MessageStringOf(x)
}
func (*Product) ProtoMessage() {}
func (x *Product) ProtoReflect() protoreflect.Message
{
mi := &file_catalog_catalog_proto_msgTypes[0]
if protoimpl.UnsafeEnabled && x != nil {
ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
if ms.LoadMessageInfo() == nil {
ms.StoreMessageInfo(mi) }
return ms
}
return mi.MessageOf(x)
}
// Deprecated: Use Product.ProtoReflect.Descriptor
instead.


func (*Product) Descriptor() ([]byte, []int) {
return file_catalog_catalog_proto_rawDescGZIP(),
[]int{0}
}
func (x *Product) GetName() string {------}
func (x *Product) GetImage() string { -------}
func (x *Product) GetCatname() string { ------}
Now let us look at Category struct code and methods:
Category struct and methods
type Category struct {
state         protoimpl.MessageState
sizeCache     protoimpl.SizeCache
unknownFields protoimpl.UnknownFields
Name string `protobuf:"bytes,1,opt,name=name,proto3"
json:"name,omitempty"`
}
func (x *Category) Reset() {
-----
}
func (x *Category) String() string {
-------
}
func (*Category) ProtoMessage() {}
func (x *Category) ProtoReflect() protoreflect.Message
{
----
}
return mi.MessageOf(x)
}
func (*Category) Descriptor() ([]byte, []int) {
-------
}
func (x *Category) GetName() string {
--------
}
ProductSpec struct and methods
type ProductSpec struct {
state         protoimpl.MessageState
sizeCache     protoimpl.SizeCache
unknownFields protoimpl.UnknownFields
Name string `protobuf:"bytes,1,opt,name=name,proto3"
json:"name,omitempty"`
}
func (x *ProductSpec) Reset() {
-------------------
}
func (x *ProductSpec) String() string {
-------------------
}
func (*ProductSpec) ProtoMessage() {}
func (x *ProductSpec) ProtoReflect()
protoreflect.Message {
--------------
}
func (*ProductSpec) Descriptor() ([]byte, []int) {
------------
}
func (x *ProductSpec) GetName() string {
--------
}
Now, let us look at ProductSpecNote struct code and methods:
ProductSpecNote struct and methods
type ProductSpecNote struct {
state         protoimpl.MessageState
sizeCache     protoimpl.SizeCache
unknownFields protoimpl.UnknownFields
Cat  *Category
`protobuf:"bytes,1,opt,name=cat,proto3"
json:"cat,omitempty"`
Spec *ProductSpec
`protobuf:"bytes,2,opt,name=spec,proto3"
json:"spec,omitempty"`
}
func (x *ProductSpecNote) Reset() {
-----------
}
func (x *ProductSpecNote) String() string {
--------
}
func (*ProductSpecNote) ProtoMessage() {}
func (x *ProductSpecNote) ProtoReflect()
protoreflect.Message {
-----
}
func (*ProductSpecNote) Descriptor() ([]byte, []int) {
------
}
func (x *ProductSpecNote) GetCat() *Category {
----
}
func (x *ProductSpecNote) GetSpec() *ProductSpec {
------
}
Proto messages are defined with the properties and accessors to
the properties. Product, Category, ProductSpec, and
ProductSpecNote messages are implemented with the properties
and accessors; serializers are defined for these message objects.


Now, let us look at the client and server stubs. The protoc tool
generates the client and server stubs code as follows:
catalog_grpc.pb.go
// Code generated by protoc-gen-go-grpc. DO NOT EDIT.


package catalog
import (
context "context"
grpc "google.golang.org/grpc"
codes "google.golang.org/grpc/codes"
status "google.golang.org/grpc/status"
)
const _ = grpc.SupportPackageIsVersion7
type CatalogClient interface {
GetProduct(ctx context.Context, in *ProductSpec, opts
...grpc.CallOption) (*Product, error)
ProductList(ctx context.Context, in *Category, opts
...grpc.CallOption) (Catalog_ProductListClient, error)
RecordProduct(ctx context.Context, opts
...grpc.CallOption) (Catalog_RecordProductClient,
error)
ProductChat(ctx context.Context, opts
...grpc.CallOption) (Catalog_ProductChatClient, error)
}
type catalogClient struct {
cc grpc.ClientConnInterface
}
func NewCatalogClient(cc grpc.ClientConnInterface)
CatalogClient {
return &catalogClient{cc}
}
func (c *catalogClient) GetProduct(ctx
context.Context, in *ProductSpec, opts
...grpc.CallOption) (*Product, error) {
out := new(Product)
err := c.cc.Invoke(ctx, "/catalog.Catalog/GetProduct",
in, out, opts...)
if err != nil {
return nil, err
}
return out, nil
}
func (c *catalogClient) ProductList(ctx
context.Context, in *Category, opts
...grpc.CallOption) (Catalog_ProductListClient, error)
{
stream, err := c.cc.NewStream(ctx,
&Catalog_ServiceDesc.Streams[0],
"/catalog.Catalog/ProductList", opts...)
if err != nil {
return nil, err
}
x := &catalogProductListClient{stream}
if err := x.ClientStream.SendMsg(in); err != nil {
return nil, err
}
if err := x.ClientStream.CloseSend(); err != nil {
return nil, err
}
return x, nil
}
type Catalog_ProductListClient interface {
Recv() (*Product, error)
grpc.ClientStream
}
type catalogProductListClient struct {
grpc.ClientStream
}
//Interface methods implemented
……..


type Catalog_RecordProductClient interface {
Send(*ProductSpec) error
CloseAndRecv() (*Product, error)
grpc.ClientStream
}
type catalogRecordProductClient struct {
grpc.ClientStream
}
//Interface methods implemented
……..


type Catalog_ProductChatClient interface {
Send(*ProductSpecNote) error
Recv() (*ProductSpecNote, error)
grpc.ClientStream
}
type catalogProductChatClient struct {
grpc.ClientStream
}
//Interface methods implemented
……..


type CatalogServer interface {
GetProduct(context.Context, *ProductSpec) (*Product,
error)
ProductList(*Category, Catalog_ProductListServer)
error
RecordProduct(Catalog_RecordProductServer) error
ProductChat(Catalog_ProductChatServer) error
mustEmbedUnimplementedCatalogServer()
}
//Interface methods implemented
……..


type UnsafeCatalogServer interface {
mustEmbedUnimplementedCatalogServer()
}
func RegisterCatalogServer(s grpc.ServiceRegistrar,
srv CatalogServer) {
s.RegisterService(&Catalog_ServiceDesc, srv)
}
//Interface methods implemented
……..


type Catalog_ProductListServer interface {
Send(*Product) error
grpc.ServerStream
}
type catalogProductListServer struct {
grpc.ServerStream
}
//Interface methods implemented
……..


type Catalog_RecordProductServer interface {
SendAndClose(*Product) error
Recv() (*ProductSpec, error)
grpc.ServerStream
}
type catalogRecordProductServer struct {
grpc.ServerStream
}
//Interface methods implemented
……..


type Catalog_ProductChatServer interface {
Send(*ProductSpecNote) error
Recv() (*ProductSpecNote, error)
grpc.ServerStream
}
type catalogProductChatServer struct {
grpc.ServerStream
}
//Interface methods implemented
……..


gRPC clients
gRPC clients exchange protobuf (protocol buffer-based)
messages with the gRPC servers. They are based on Google
protocol for serialization and deserialization. Client libraries can
be generated using the protoc tool. Memory schema is loaded
by the gRPC services based on the Proto file configuration
(catalog.proto). In memory schema, messages can be
serialized and deserialized based on the binary messages.


Streaming messages can be handled by gRPC Server and clients.


The direction of streaming can be in inward and outward
directions. Catalog client has RecordProduct and ProductChat
methods which talk to the gRPC server.


1. First, let us look at the code for gRPC clients:
client.go
func printProduct(client cat.CatalogClient, point
*cat.ProductSpec) {
log.Printf("Getting feature for product (%d, %d)",
point.Name)
ctx, cancel :=
context.WithTimeout(context.Background(),
10*time.Second)
defer cancel()
product, error := client.GetProduct(ctx, point)
if error != nil {
log.Fatalf("client.GetProduct failed: %v", err)
}
log.Println("Client: Received from server - " +
"product name " + product.Name + " ,Image " +
product.Image)
}
Now,
let
us
look
at
the
PrintProducts
method
implementation:
PrintProducts method
func printProducts(client cat.CatalogClient, rect
*cat.Category) {
log.Printf("Client: Looking for products on the
server within %v", rect)
ctx, cancel :=
context.WithTimeout(context.Background(),
10*time.Second)
defer cancel()
stream, err := client.ProductList(ctx, rect)
if err != nil {
log.Fatalf("client.ProductList failed: %v", err)
}
for {
product, error := stream.Recv()
if error == io.EOF {
break
}
if error != nil {
log.Fatalf("client.ProductList failed: %v", err)
}
log.Printf("Product: name: %q, product:(%v)",
product.GetName(),
product.GetImage())
}
}
Now,
let
us
look
at
the
implementation
of
the
RecordProduct method:
RecordProduct method
func RecordProduct(client cat.CatalogClient) {
timeInNanos := time.Now().UnixNano()
newSource := rand.NewSource(timeInNanos)
ran := rand.New(newSource)
ranInt := ran.Int31n(100)
pointCount := int(ranInt) + 2
var points []*cat.Product
for i := 0; i < pointCount; i++ {
points = append(points, randomProduct(r))
}
log.Printf("client: catalog has %d products
created.", len(points))
context, cancel :=
context.WithTimeout(context.Background(),
10*time.Second)
defer cancel()
stream, error := client.RecordProduct(context)
if error != nil {
log.Fatalf("client.RecordProduct failed: %v",
error)
}
for _, pointer := range points {
log.Printf("client sending product" +
pointer.Name)
if error := stream.Send(&cat.ProductSpec{Name:
pointer.Name}); error != nil {
log.Fatalf("client.RecordProduct: stream.Send(%v)
failed: %v", pointer, error)
}
}
reply, error := stream.CloseAndRecv()
if error != nil {
log.Fatalf("client.RecordPoduct failed: %v", err)
}
log.Printf("Produuct summary: %v", reply)
}
Now, let us look at ProductChat method implementation:
ProductChat method
func ProductChat(client cat.CatalogClient) {
notes := []*cat.ProductSpecNote{
{Cat: &cat.Category{Name: "Athletics"},
Spec: &cat.ProductSpec{Name: "pump"}},
}
context, cancel :=
context.WithTimeout(context.Background(),
10*time.Second)
defer cancel()
stream, error := client.ProductChat(context)
if error != nil {
log.Fatalf("client.RouteChat failed: %v", err)
}
waitch := make(chan struct{})
go func() {
for {
in, error := stream.Recv()
if error == io.EOF {
close(waitch)
return
}
if error != nil {
log.Fatalf("client.ProductChat failed: %v", err)
}
log.Printf("Client Got message %s at product(%d)",
in.Cat.Name, in.Spec.Name)
}
}()
for _, noter := range notes {
if error := stream.Send(noter); error != nil {
log.Fatalf("client.ProductChat: stream.Send(%v)
failed: %v", noter, error)
}
}
stream.CloseSend()
<-waitch
}
Now, let us look at randomProduct method implementation:
randomProduct method
func randomProduct(r *rand.Rand) *cat.Product {
name := "Product" + strconv.Itoa(r.Intn(180))
img := name + ".jpg"
categ := "General"
return &cat.Product{Name: name, Image: img,
Catname: categ}
}
Now, let us look at the main method implementation:
main method
func main() {
flag.Parse()
var opts []grpc.DialOption
opts = append(opts,
grpc.WithTransportCredentials(insecure.NewCredenti
als()))
conn, error := grpc.Dial(*serverAddr, opts...)
if error != nil {
log.Fatalf("fail to dial: %v", error)
}
defer conn.Close()
client := cat.NewCatalogClient(conn)
printProduct(client, &cat.ProductSpec{Name:
"cycle"})
printProduct(client, &cat.ProductSpec{Name:
"pump"})
printProducts(client, &cat.Category{Name:
"Electronics"})
RecordProduct(client)
ProductChat(client)
}
2. Random products are created with their spec and sent as
requests to add the product to the catalog. CatalogClient
is created first and passed on to the interface methods.


The command to create a module proto is:
//inside proto directory
go mod init golang-db
go mod tidy
3. Then, the module is built using the command:
go build
4. We need a server to connect to from the client. Let us see
how to build the gRPC server before executing the client.


gRPC server
The gRPC server is implemented using the stubs created in
catalog.grpc.pb.go. The service methods are implemented in
the server.


server.go
type catalogServer struct {
cat.UnimplementedCatalogServer
savedProducts []*cat.Product
mu               sync.Mutex
productSpecNotes map[string][]*cat.ProductSpecNote
}
func (s *catalogServer) GetProduct(ctx
context.Context, point *cat.ProductSpec)
(*cat.Product, error) {
for _, feature := range s.savedProducts {
if feature.Name == point.Name {
return feature, nil
}
}
return &cat.Product{Name: point.Name, Image:
point.Name + ".jpg"}, nil
}
func (s *catalogServer) ProductList(rect
*cat.Category, stream cat.Catalog_ProductListServer)
error {
for _, feature := range s.savedProducts {
if feature.Catname == rect.Name {
if error := stream.Send(feature); error != nil {
return err
}
}
}
return nil
}
func (s *catalogServer) RecordProduct(stream
cat.Catalog_RecordProductServer) error {
var pointCount, featureCount int32
for {
point,
err := stream.Recv()
if err == io.EOF {
return stream.SendAndClose(&cat.Product{
Name:    "Completed",
Image:   ".jpg",
Catname: "Messages",
})
}
if error != nil {
return error
}
pointCount++
for _, features := range s.savedProducts {
log.Printf("server: recorded " + point.Name)
if features.Name == point.Name featureCount++
}
}
}
}
Now, let us look at the ProductChat method implementation:
Product Chat method
func (s *catalogServer) ProductChat(stream
cat.Catalog_ProductChatServer) error {
for {
in, error := stream.Recv()
if error ==
io.EOF {
return nil
}
if error !=
nil {
return error
}
keyin := serializeProductSpec(in)
s.mu.Lock()
s.productSpecNotes[keyin] =
append(s.productSpecNotes[keyin], in)
rn := make([]*cat.ProductSpecNote,
len(s.productSpecNotes[keyin]))
copy(rn, s.productSpecNotes[keyin])
s.mu.Unlock()
for _, note := range rn {
if error := stream.Send(note); err != nil {
return error
}
}
}
}
Now, let us look at the loadProductSpec method
implementation:
loadProductSpecs method
func (s *catalogServer) loadProductSpecs(filePath
string) {
var data []byte
if filePath != "" {
var err error
data, err = os.ReadFile(filePath)
if error != nil {
log.Fatalf("Failed to load default features: %v",
error)
}
} else {
data = exampleData
}
s.savedProducts = make([]*cat.Product, 0)
if err := json.Unmarshal(data, &s.savedProducts);
error != nil {
log.Fatalf("Failed to load default features: %v",
error)
}
}
Now, let us look at the implementation of the
serializeProductSpec method:
serializeProductSpec method
func serializeProductSpec(point *cat.ProductSpecNote)
string {
return fmt.Sprintf("%d", point.Spec.Name)
}
func serialize(point *cat.Product) string {
return fmt.Sprintf("%d %d", point.Name, point.Image)
}
Now, let us look at the newServer method implementation:
newServer method
func newServer() *catalogServer {
s := &catalogServer{productSpecNotes: make(map[string]
[]*cat.ProductSpecNote)}
s.loadProductSpecs(*jsonDBFile)
return s
}
Now, let us look at the main method implementation:
Main method
func main() {
flag.Parse()
lis, err := net.Listen("tcp",
fmt.Sprintf("localhost:%d", *port))
if err != nil {
log.Fatalf("failed to listen: %v", err)
}
var opts []grpc.ServerOption
grpcInstServer := grpc.NewServer(opts...)
cat.RegisterCatalogServer(grpcInstServer, newServer())
grpcInstServer.Serve(lis)
}
The example data is initialized below:
var exampleData = []byte(`[{"name": "cycle", "image" :
"cycle.jpg", "catname" : "Athletics"}, {"name":
"pump", "image" : "pump.jpg","catname" : "Athletics"},
{"name": "Product3", "image" :
"product3.jpg","catname" : "Electronics"}]`)
The CatalogServer is instantiated and registered with the gRPC
registry as a Catalog server. When the new server is instantiated,
the products are loaded into the catalog with their category.


gRPC catalog server has implementations for getProduct,
ProductList, RecordProduct, and Product Chat methods. It also
has the loadProductSpec method to load the catalog products.


Recording of the product requests to add to the catalog happens
through the Record Product. ProductChat streams the
information requested.


You can now compile and run the gRPCclient.go. and
gRPCserver.go. Let us start with the server with the command
as follows:
go run gRPCserver/gRPCserver.go
The output will be as follows:
Figure 6.4: Server output
In the output, you can see the recording of the product requests.


These requests come from the client. After the server is
launched, you can run the client.


You can now compile and run the client.go. The command is as
shown below:
go run server/client.go
The output will be as shown in the following figure:
Figure 6.5: Client output
The output shows the productSpec requests sent from the client
to the server. The message of finishing the stream requests and
lookup for a product is shown in the output from the client side.


Conclusion
In this chapter, we have covered topics related to gRPC
applications, protocol buffers, clients, and servers. Examples
were presented to demonstrate how gRPC servers and clients
can be built using Go Lang.


In the next chapter, the readers will know how to build a real-life
CRM application. CRM application will be a web application that
will have the capability to maintain customer data and run
campaigns for segments or a list of customers by geography or
vertical.


Points to remember
Here are some points to remember from this chapter:
Remote procedure call-based applications can be built
using gRPC frameworks.


Go language can be used to build Remote procedures and
call-based applications.


Protocol buffers are used to generate messages, and
server-side and client-side stubs to be used by the gRPC
server and client.


gRPC services are defined in .proto file.


gRPC services are implemented in gRPC clients and servers
after generating stubs using the protocol tool.


gRPC server listens to clients sending requests, and the
server processes the remote procedure calls to reply back
with a response.


Join our book’s Discord space
Join the book's Discord Workspace for Latest updates, Offers,
Tech happenings around the world, New Release and Sessions
with the Authors: <AUTHORS>