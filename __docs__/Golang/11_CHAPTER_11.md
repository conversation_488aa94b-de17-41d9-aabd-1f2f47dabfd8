# 11 CHAPTER 11 Go Microservices

microservices architecture versus the monolithic architecture principles.

create an architecture using microservices and deploy these services on the containers.

Go Lang microservices architecture styles and containerization:

    Monolithic architecture
    Microservices architecture
    Microservices in Go
    Containerized Go microservices

Containers can be used to containerize the Go microservices for easy deployment on the cloud.

Before microservices, the architectural style was monolithic architecture style. In monolithic architecture, layered
architectural patterns are used to separate presentation,
business logic, middleware, and persistence layers.


Microservices architecture style focuses on building services
for applications and developing the application for a set of
services first. The tech stack can be different in the
microservices, but protocols like REST help in integrating
from the front end with back microservices.


Go Lang tech stack can be used to build microservices. Let
us first look at monolithic architecture.


Monolithic architecture
In monolithic architecture, the choice of tech stack and data
store is very important to start with. Once the choice is
made, different layers are built using the frameworks
available in the tech stack. The user interface will be in the
presentation layer. Business rules will be in the business
logic layer. The middleware and persistence layers operate
with the database and other data sources. The database can
be relational or NoSQL-based.


Different layers are deployed on different nodes, and they
interact on the secured network.


Scaling can be done for each layer based on the type of the
application. The monolithic application can be deployed
using DevOps tools. Managing changes and deploying is
important. DevOps Research and Assessment metrics can
help in measuring the rate of change and deployment. The
metrics are as follows:
Deployment Frequency (DF)
Mean Lead Time for Change (MLT)
Mean Time to Recover (MTTR)
Change Failure Rate (CFR)
Reliability
These metrics help measure velocity, stability, and
operational performance. They help in measuring the
software development velocity. They are used for making key
decisions to come up with new process initiatives and
changes to the existing team members. They are used to
create teams that are cross-functional and small in size. A
set of new requirements or changes in functionality are
developed and deployed using the DevOps toolkit.


These metrics help determine the performance of the team
members. Deployment frequency measures the number of
releases deployed on the live environment. The mean lead
time for changes is calculated using the cycle time from the
development to the deployment on the live environment.


The mean time to recovery is measured using the time for
getting the live environment from shut down after the crash
to up-and-running mode. The change failure rate is the count
of the occurrences of the live environment going down
because of deployments of new requirement-related code.


Reliability helps in gauging the application performance,
scalability, latency, failover, and availability.


In the case of a CRM application, it consists of creating
customers and managing customer data. Campaigns will be
created and targeted for publishing to the customers. The
leads are gathered as a list of customers or segments of
customers. Customer segments are created by attributes like
age, gender, salary range, or geography. When the customer
likes the product and buys the product, a transaction is
logged in the back office of a retail store or an e-commerce
company. The transactions are processed to check if the lead
has been realized and turned into revenue. This process
helps assess lead conversion. Transactions can also be
checked for aberration to identify the behavior of the
customer.


The other important feature is the loyalty points, which have
different levels like gold, silver, and bronze levels. Customer
buys the products and loyalty points are stored in the system
to track the progress of the customer. Offers are targeted
based on loyalty to improve the purchasing rate of the
customer. The discounted offers are shared based on where
the customer is in terms of the loyalty points. Companies
frequently announce loyal customers and convert leads from
the CRM portal.


The application consists of use cases, business logic,
business rules, and persistent data. The architecture can be
monolithic, which is a layered architecture. The layers can be
presentation, business rules, middleware, and persistence
layers. Middleware can integrate with external applications
using synchronous and asynchronous mechanisms.


You can also have monolithic applications with different
presentation-related components, business logic, rules
components, and persistent components. All are built and
bundled into a single monolithic app. In the Java tech stack,
this app can be a web archive or enterprise archive. You can
have a cluster of servers with a load balancer to balance the
load across the cluster nodes.


These monolithic applications are deployed on servers. The
servers will have an operating system, and the system-
related services will be running. They are as follows:
Memory management
Process scheduling
Device drivers
File I/O
Process management
Process monitoring
The monolithic architecture was popular as it minimizes the
communication overhead across the system processes by
running a single deployable package in a process. A single
deployable package helps in simplifying the overall design of
the application. The problems are related to security and
stability. Maintenance and customization are other issues of
monolithic applications. Monolithic architecture does not
provide the flexibility for upgrades and changes. The
components in the package are tightly coupled. It will run in
a single process, and memory will be shared across the
components. The development of new requirements and
enhancements will be tough. The reliability of the monolithic
app is low, as a single service error might put down the
whole process.


Now, let us look at microservice architecture.


Microservices architecture
You want to build an application that has functional
requirements. How do you start designing and architecting
using microservices patterns? The services are identified by
the architects and leads involved in the high-level design.


These services can be of different tech stacks, but they have
designed interfaces/APIs/protocols to process and respond
with the required information. The clients can be from a
mobile app, web app, desktop app, or another application’s
API invoking these services. Microservices architecture
consists of patterns from different methodologies like
domain-driven design, polyglot-based services, persistence
mechanisms, and continuous delivery.


Microservices architecture is based on single-responsibility
principles. The single responsibility principle was created by
Robert C. Martin. According to this principle, group the items
that get modified for the same cause and isolate the items
that get modified for different causes. Microservices
architecture uses these principles to identify the functionality
of a microservice. Microservices can talk to each other and
address the functional requirements.


Microservices architecture is popular because the
microservices can be built by different programmers and in
different tech stacks. They can talk to each other using the
same protocols as REST/SOAP/etc. These microservices can
be deployed on different servers. Unlike monolithic
architecture, microservices would not fail with a single error
in a microservice.


You can start working on microservices architecture by
identifying similar business requirements and grouping them
into logical modules first. After identifying logical modules,
the next step is to find a microservice that can serve the
business functions. The microservice needs to give a
business value to the target user. To give an example of an
e-commerce application, we can start with a catalog service,
inventory service, order creation service, order delivery
service, user creation service, user profile service,
recommendation service, and user product review service. A
team can be created for each service and can be built based
on the skills available to the team members. The services
developed should have the capability to communicate with
selected and specified protocols like HTTP, REST, SOAP, and
others.


Data stores can be relational if there is a structure in the
data. No SQL database can be used if there is no structure in
the data. The deployment can be done by any DevOps tool,
which is based on continuous integration and
continuous development (CI/CD). Microservices
architecture helps decentralize an enterprise. The
microservices can communicate using a message queue
enterprise service bus or an orchestration engine. The
services communicate through messages, and messages are
routed to different endpoints of the services. Deployment
can happen on the cloud instances or in different virtual
machines on the in-house servers. Containers also play an
important role in packaging microservices with the tech
stack required for deployment. Swagger documentation,
postman collection, or a wiki page can help in documenting
the microservices. API gateway pattern can be used to
provide an entry point for service clients on mobile, web,
desktop, and other APIs. Microservice discovery and security
are important architectural aspects in an enterprise.


Providing resiliency is an important architectural aspect of
microservices-based enterprise applications. The bulkhead
pattern helps in separating the application aspects into
different groups and provides failover if one of the groups
fails. Circuit breaker pattern is also used to manage failures.


In this pattern implementation, thresholds/limits are set to
ensure that the failure is detected and all further service
requests are responded to with an error. The timeouts are
also configured for multiple retries. For logging, a
centralization approach is used, and tools like Elasticsearch,
Kibana, Logstash, and logging daemon are part of the
solution. Log and statistics aggregation is another pattern
used for the centralization of logging. Now, let us look at the
microservices-based app tech stack:
Figure 11.2: Microservices context
Now, let us look at building a microservices architecture-
based application and services. We are revisiting the
application we discussed in the previous chapter. In that
application, web links can be loaded to analyze the website's
metadata. We can create an RSS feed for the website,
looking at various topics and content covered. RSS feeds can
be updated frequently based on website updates. This
application can be built using the Microservices architecture
style.


You can have web links configured based on your
preferences and the topics selected. Web links can be
scanned to generate RSS feeds topic-wise. The application
will have the capability to add or remove the web links from
the topics. New topics can be added for the application with
web links. RSS feeds can be loaded into the application
based on the topic. Feeds can be stored in a NoSQL database
using a REST-based persistence service for feeds. This
application can be on the cloud serving as a software as a
service.


Users can register using social authentication REST service,
which authenticates with Google/MSN/yahoo/Twitter. Users
can create a profile and preferences with new REST services
for profiles and preferences for users. Feeds can be stored in
the NoSQL database and can be categorized by topic using
the categorization REST service. They can be tagged with
keywords and made searchable for quick access. Cache
service provides the mechanisms that can be used to
improve the retrieval time of the master data or user
preferences and interests.


Another way to speed up the performance is by having in-
memory user-specific feeds for a category selected loaded.


Users can browse them quickly. Scalability is easier as each
of the services we discussed can be deployed on different
servers. Concurrent users can browse the feeds quickly
based on the category selection. In memory, multiple
categories and feeds specific to that category can be loaded.


User profiles can be modified, and demographics are stored
in the NoSQL database using the user profile microservice.


The recommendation REST service helps recommend feeds
for users. Users can access correlated feeds read by other
users. Tags can be selected for feeds, and users can add new
tags to the existing tagged feeds.


Typical demographics of the user gathered are age, gender,
interests, preferences, job/profession, designation, and
others. A recommendation system can be built as a separate
microservice that can be added to the system to provide new
feeds and tags for users. Users will be eager to come back to
the application because new content will be ready to be
processed as recommendations. This can be like AdSense,
where the context of the user can be used based on the
reader’s browsing history of the feeds.


User satisfaction is dependent on the user profile, and other
factors are user profile, content, context, interests, etc. The
recommended feeds are evaluated for performance based on
factors such as click-through rate, stay time, upvotes,
comments, and reposts. Content provided to the user can be
textual information, images/posters, videos, web links, and
documents. This content can have multiple labels. The user-
specific labels can be a category, topic, keyword, source,
community, or vertical labels. User profile-specific labels
include gender, age, location, preferences, interests,
favorites, and bookmarks. User behavior-specific labels can
be browsing time, reading time, staying time, and click-
through analysis. Label generation can be done by using
methods like noise canceling by filtering the content,
penalizing the trends on feeds, using time decaying and
browsing history, using global bias and click rate, and
likes/dislikes/unsubscribes.


In the example above, we have identified the following
microservices:
User Social Authentication Service
User Profile Service
User Preferences Service
User Recommendation Service
Feeds Storage Service
User Registration Service
Microservices in Go
Now, let us revisit the CRM application built on microservices
architecture. We used the Beego web framework to develop
the CRM web application and REST API. Beego framework is
an open-source framework that has application tools, an
object relationship mapping framework, and other packages.


ORM framework brings down the ordeal of developing the
CRM relational schema and mapping it to object classes and
names of the attributes. Beego is used to build CRM
microservices, which are REST APIs. The microservices are
integrated with open-source JavaScript frontend frameworks.


The CRM Beego web application was built using the Beego
microservices and SQLite database. Beego framework helps
develop web apps at an affordable cost.


First, let us look at the code for Go Lang microservices built
using Beego REST API:
main.go
package main
import (
"fmt"
"github.com/gorilla/handlers"
"go_beego_REST_api/pkg/db"
handler "go_beego_REST_api/pkg/handlers"
"log"
"net/http"
"github.com/gorilla/mux"
)
func main() {
r := mux.NewRouter()
DB := db.InitializeDB()
r.HandleFunc("/customers",
handler.GetCustomers(DB)).Methods("GET")
r.HandleFunc("/create",
handler.CreateCustomer(DB)).Methods("POST")
r.HandleFunc("/update",
handler.UpdateCustomer(DB)).Methods("PUT")
r.HandleFunc("/delete",
handler.DeleteCustomer(DB)).Methods("DELETE")
fmt.Println("Server at 8080")
log.Fatal(http.ListenAndServe(":8080",
handlers.CORS()(r)))
}
The following figure shows the Beego web app directory
structure:
Figure 11.3: Microservices-based application code artifacts
The above main.go has the code for initializing the ORM and
registering the model. Beego is started in the main method.


This microservice is integrated with the web application.


The following is the code for back-end ORM database calls
for microservice endpoints.


db.go
package db
import (
"fmt"
"github.com/jinzhu/gorm"
_
"github.com/jinzhu/gorm/dialects/postgres"
)
func InitializeDB() *gorm.DB {
db, err := gorm.Open("postgres",
"user=newuser password=newuser dbname=crm
sslmode=disable")
if err != nil {
fmt.Println(err)
} else {
fmt.Println("DB connected!")
}
return db
}
Handler.go has the code for tableName definition and the
object class specification. Now, let us look at the handler.go
code:
handler.go
package handler
import (
"encoding/json"
"fmt"
"net/http"
"github.com/jinzhu/gorm"
)
type CustomerBody struct {
Name string `json"name"`
}
type Customer struct {
Id      int    `json:"id"`
Name    string `json:"name"`
Mobile  string `json:"mobile"`
Address string `json:"address"`
}
func GetCustomers(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var customers []Customer
_ =
db.Table("customer").Select("id,
name,mobile,address").Scan(&customers)
json.NewEncoder(w).Encode(customers)
}
}
func CreateCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var RequestBody CustomerBody
json.NewDecoder(r.Body).Decode(&RequestBody)
_ =
db.Table("customer").Create(&RequestBody)
fmt.Println("Created Customer")
json.NewEncoder(w).Encode(RequestBody)
}
}
func UpdateCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var PutBody Customer
json.NewDecoder(r.Body).Decode(&PutBody)
_ =
db.Table("customer").Where("id=?",
PutBody.Id).Update("name",
PutBody.Name).Scan(&PutBody)
fmt.Printf("Updated Customer with
id %d\n", PutBody.Id)
json.NewEncoder(w).Encode(PutBody)
}
}
func DeleteCustomer(db *gorm.DB) http.HandlerFunc {
return func(w http.ResponseWriter, r
*http.Request) {
w.Header().Set("Content-Type",
"application/json")
var DeleteBody Customer
json.NewDecoder(r.Body).Decode(&DeleteBody)
_ =
db.Table("customer").Delete(&DeleteBody)
fmt.Printf("Deleted Customer with
id %d\n", DeleteBody.Id)
json.NewEncoder(w).Encode(DeleteBody)
}
}
Now, let us look at the database script to create a table in
the Postgres CRM database. You can use the following
commands to create a Postgres database CRM and users:
psql -h localhost -d postgres
----
ALTER USER postgres PASSWORD 'postgres';
CREATE USER newuser with PASSWORD 'newuser'
CREATEDB;
select * from users;
\du
\l
\q
-----
createdb crm
psql -h localhost -d postgres
\c crm
CREATE TABLE "customer" (
"id"  SERIAL,
"name" varchar(200) NOT NULL,
"mobile" varchar(100),
"address" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
\dt
\du
alter role newuser superuser;
create user newuser with password 'newuser';
grant all privileges on database crm to newuser;
alter role newuser superuser;
Now, let us look at the Database SQL for creation of the
schema:
Database
CREATE TABLE "customer" (
"id" serial,
"name" varchar(200) NOT NULL,
"mobile" varchar(100),
"address" varchar(400) DEFAULT NULL,
"notes" text,
UNIQUE (name)
);
create user newuser with password 'newuser';
grant all privileges on database crm to newuser;
Now, let us look at the routes configured in index.go for the
microservice.


The following table shows the routes/paths mentioned in the
index.go. These paths are for customer management
microservices.


Action
Controller
method
Http
method
URL route
Description
Create
Add
POST
/create
Create a new
customer
Read
getAll
GET
/customers
Retrieve list of
customers
Delete
Delete
DELETE
/delete
Delete a
customer
Update
Update
PUT
/update
Update a
customer
Table 11.1: Routes and Paths Mapping
You can run the Beego REST API server by using the following
commands:
go mod init go_beego_REST_api
go mod tidy
The output of the REST API server execution is as follows:
Figure 11.4: Output of the REST API server execution
Now, let us look at another example where a microservice
communicates with a message Queue. We have seen this
example in the previous chapter. Let us revisit this in the
microservices context. We built this Go Lang microservice in
Chapter 4, Building REST API. In this context of building Go
Service docker, let us relook at how to develop REST API,
which can interact with message queues. We are going to
use Gin and RabbitMQ. Now, let us look at the Gin REST API
interacting with web applications and the database:
Figure 11.5: Go Lang microservices based on Gin REST API
Now, let us look at the code for developing Microservices
based on REST API using Gin and RabbitMQ:
main.go
package main
import (
"fmt"
"github.com/gin-gonic/gin"
"github.com/rs/zerolog/log"
"REST_api_mq/producer/config"
"REST_api_mq/producer/utils"
)
func init() {
mode := utils.GetEnvVar("GIN_MODE")
gin.SetMode(mode)
}
func main() {
appGin := config.CreateApp()
addrGin := utils.GetEnvVar("GIN_ADDR")
portGin := utils.GetEnvVar("GIN_PORT")
log.Info().Msgf("App is up at
http//:%s:%s", addrGin, portGin)
if error := appGin.Run(fmt.Sprintf("%s:%s",
addrGn, portGin)); error != nil {
log.Fatal().Err(error).Msg("Http
Server setup failed")
}
}
Now, let us look at the createApp to see how the routes are
set up through routers:
Creating_app.go
package config
import (
"github.com/gin-gonic/gin"
"github.com/rs/zerolog/log"
"REST_api_mq/producer/middlewares"
"REST_api_mq/producer/routers"
)
func CreateApp() *gin.Engine {
log.Info().Msg("service starting")
app := gin.New()
app.Use(gin.Recovery())
app.SetTrustedProxies(nil)
log.Info().Msg(" cors, request id, request
logging middleware added")
app.Use(middlewares.CORSMiddleware(),
middlewares.RequestID(),
middlewares.RequestLogger())
log.Info().Msg("routers setup")
routers.SetupRouters(app)
return app
}
The router package has the routes mapped to methods that
talk to the message queue:
Get   /ping     controllers.ping
Post /publish/example controllers.Example
Now, let us look at the setup.go code.


setup.go
package routers
import (
"github.com/gin-gonic/gin"
"REST_api_mq/producer/controllers"
)
func CreateRouters(engine *gin.Engine) {
version1 := engine.Group("/v1")
{
version1.GET("/ping",
controllers.Ping)
version1.POST("/publish/example",
controllers.Example)
}
}
The controllers package has the message publisher
configured to publish messages to a message queue. Now,
let us look at the controllers.go code:
controllers.go
package controllers
import (
"net/http"
"github.com/gin-gonic/gin"
"github.com/rs/zerolog/log"
"REST_api_mq/producer/environment"
"REST_api_mq/producer/models"
"REST_api_mq/producer/utils"
func Example(context *gin.Context) {
var msg models.Message
request_id := context.GetString("x-request-
id")
if binderr := context.ShouldBindJSON(&msg);
binderr != nil {
log.Error().Err(binderr).Str("request_id",
request_id).


Msg("Error occurred while
binding request data")
context.JSON(http.StatusUnprocessableEntity, gin.H{
"message": binderr.Error(),
})
return
}
connectionString :=
utils.GetEnvVar("RMQ_URL")
producer := utils.MessagePublisher{
environment.EXAMPLE_QUEUE,
connectionString,
}
producer.PublishMessage("text/plain",
[]byte(msg.Message))
context.JSON(http.StatusOK, gin.H{
"response": "Message received from
REST API",
})
}
MessagePublisher is a struct defined in the utils package.


Its method publishes messages to a queue defined in the
environment. Now, let us look at the publisher.go code:
Publisher.go
package utils
import (
"github.com/rs/zerolog/log"
"github.com/streadway/amqp"
)
type MessagePublisher struct {
Queue            string
ConnectionString string
}
func (x MessagePublisher) OnError(err error, msg
string) {
if err != nil {
log.Err(err).Msgf("Publishing
message error '%s' queue. Error message: %s",
publisher.Queue, msg)
}
}
func (publisher MessagePublisher)
PublishMessage(contentType string, body []byte) {
conn, error :=
amqp.Dial(publisher.ConnectionString)
publisher.OnError(error, "RabbitMQ not
connected")
defer conn.Close()
channel, err := conn.Channel()
publisher.OnError(err, "Channel not
opened")
defer channel.Close()
q, error := channel.QueueDeclare(
publisher.Queue,
false,
false,
false,
false,
nil,
)
publisher.OnError(error, "Queue Not
declared")
error = channel.Publish(
"",
q.Name,
false,
false,
amqp.Publishing{
ContentType: contentType,
Body:        body,
})
publisher.OnError(error, "message not
published")
}
Now, let us look at the consumer package:
main.go
package main
import (
"REST_api_mq/consumer/environment"
"REST_api_mq/consumer/handlers"
"REST_api_mq/consumer/utils"
)
func main() {
connectionString :=
utils.GetEnvVar("RMQ_URL")
messageQueue := utils.MessageConsumer{
enviornment.EXAMPLE_QUEUE,
connectionString,
handlers.HandleMessaage,
}
forever := make(chan bool)
go messageQueue.Consume()
<-forever
}
MessageConsumer is created from the utils package:
Message_consumer.go
package utils
import (
"github.com/rs/zerolog/log"
"github.com/streadway/amqp"
)
type MessageConsumer struct {
Queue            string
ConnectionString string
MsgHandler       func(queue string, msg
amqp.Delivery, err error)
}
func (consumer MessageConsumer) OnError(errors
error, msg string) {
if errors != nil {
consumer.MsgHandler(consumer.Queue,
amqp.Delivery{}, errors)
}
}
func (consumer MessageConsumer) Consume() {
conn, err :=
amqp.Dial(consumer.ConnectionString)
consumer.OnError(err, "Failed to connect to
RabbitMQ")
defer conn.Close()
channel, err := conn.Channel()
consumer.OnError(err, "Failed to open a
channel")
defer channel.Close()
q, err := channel.QueueDeclare(
consumer.Queue,
false,
false,
false,
false,
nil,
)
consumer.OnError(err, "Failed to declare a
queue")
msgs, err := channel.Consume(
q.Name,
"",
true,
false,
false,
false,
nil,
)
consumer.OnError(err, "Failed to register a
consumer")
forever := make(chan bool)
go func() {
for delivery := range msgs {
consumer.MsgHandler(consumer.Queue, delivery, nil)
}
}()
log.Info().Msgf("Started listening-
messages from '%s' queue", consumer.Queue)
<-forever
}
The environment package has the constants defined for the
Environment file, directory, and message queue. Now, let us
look at the constants.go code:
constants.go
package environment
const ENV_FILE = ".env"
const ENV_FILE_DIRECTORY = "."
const EXAMPLE_QUEUE = "message_queue"
.env has the Rabbit message queue configuration URL and
Log level. Now, let us look at the environment file:
.env
LOG_LEVEL = debug
RMQ_URL = amqp://guest:guest@localhost:5000/
Each microservice shown above is an independent
deployable unit that can be tested and loosely coupled.


Microservice can consist of small services built and used in
the framework for different features. A small team can build
microservices architecture-based applications very easily
and quickly. Agile process and microservices go together well
in making the development of features meet the time to
market. Polyglot microservices architecture style embraces
different tech stacks like Java, Python, Go Lang, PHP, and
.NET.


Microservice architecture provides benefits such as code
readability, component decentralization, continuous
integration, test automation, continuous deployment, and
domain-driven design.


Containerized Go microservices
Containers help in packaging microservices and make them
portable. The isolation of the microservices can be done by
using containers like Docker and Kubernetes. Microservices
that are modular can be deployed on different containers
and scaled separately based on the load requirements for
each microservice. Containers help in making microservices
more agile, flexible, and scalable. They can be enhanced and
maintained easily for upgrades and new requirements. There
are issues with containers that are related to authorization,
authentication, monitoring, and data consistency of the
microservices. Containers can be orchestrated using
Kubernetes or Docker Swarm, which provide features such as
service monitoring, load balancing, service discovery, and
service management. Container-based microservices can
talk to each other using messaging/event-driven
architecture/API gateways/service discovery patterns. Data
consistency can be achieved in container-based
microservices by using patterns like event sourcing, eventual
consistency, database per microservice, and distributed
transactions. Security in containers can be improved using
secure containers, role-based access controls, data
encryption, and network security. Now, let us look at the
containerized microservices and their features:
Figure 11.6: Containerized microservices
Kubernetes is a popular container for cluster pod capability. A
Kubernetes pod wraps the container instances. A pod with an
IP address can talk to other pods in the Kubernetes cluster.


Microservices-based architecture can be implemented using
a pod that has an app container, message broker, and proxy
sidecar container to talk to the other microservices. A
microservices container is different from a web container or
an application server container. Container-based
microservices perform well in maintaining the CPU/Disk/IO
utilization for the load requirements.


We looked at the containerized Go REST API. Let us revisit
this example in the context of microservices. Docker is used
for the deployment of the Go Lang microservices. In the
following example, we will look at how to develop REST API
(dockerized microservice), which can interact with message
queues. We are going to use Gin and RabbitMQ.


The environment package has the constants defined for the
Environment file, directory, and message queue. Now, let
us look at the constants.go code:
constants.go
package environment
const ENV_FILE = ".env"
const ENV_FILE_DIRECTORY = "."
const EXAMPLE_QUEUE = "message_queue"
.env will Rabbit message queue configuration URL
and Log level.


.env
LOG_LEVEL = debug
RMQ_URL = amqp://guest:guest@localhost:5000/
Now, let us look at the docker-compose.yaml file:
docker-compose.yaml
networks:
rabbitmq-example:
driver: bridge
services:
rabbitmq:
image: 'rabbitmq:3-management'
networks:
- rabbitmq-example
volumes:
- ./rabbit-
mq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
ports:
- "8080:15672"
healthcheck:
test: [ "CMD", "rabbitmqctl", "status"]
interval: 5s
timeout: 15s
retries: 5
producer:
build: ./producer
ports:
- "5050:5050"
networks:
- rabbitmq-example
depends_on:
- rabbitmq
environment:
GIN_MODE: "release"
GIN_HTTPS: "false"
GIN_ADDR: "0.0.0.0"
GIN_PORT: "5050"
LOG_LEVEL: "debug"
RMQ_URL: "amqp://guest:guest@rabbitmq:5673/"
consumer:
build: ./consumer
networks:
- rabbitmq-example
depends_on:
- rabbitmq
RESTart: on-failure
environment:
LOG_LEVEL: "debug"
RMQ_URL: "amqp://guest:guest@rabbitmq:5673/"
You can now compile and run the rabbitmq, producer, and
consumer services in the docker. The command to run the
Docker is as follows:
docker-compose up
The output will be as follows:
Figure 11.7: Output of Docker Compose Up command
In the above example, we have used docker-compose to
build a Go Lang microservice based on MQ.


Conclusion
In this chapter, we have covered topics related to monolithic
architecture, Microservices architecture, Go Lang-based
microservices, and containerized Go Lang microservices.


Monolithic applications are architected by creating a package
of components that can be deployable on a server in a single
process. Microservices architecture helps in building
distributed microservices deployed on different servers and
can communicate using REST/HTTP/SOAP/Messaging. Go
Lang frameworks Beego, Gin, and others can be used to
build microservices-based applications. Docker and
Kubernetes can be used to package the Go Lang
microservices.


In the next chapter, the readers will learn how to build
security services like authentication, authorization,
encryption, and identity management. The readers will know
about Cross-Site Request Forgery (CSRF) prevention
middleware. The readers can understand the security
principles to build scalable and secure web applications.

