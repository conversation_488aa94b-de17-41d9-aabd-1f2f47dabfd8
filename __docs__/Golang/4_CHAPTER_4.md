# 4 CHAPTER 4 Building REST API


The chapter covers the following topics:

    Algorithms
    REST API in Go
    REST API interacting with relational database
    REST API interacting with No SQL database
    REST API interacting with message queues
    Go best practices


# Algorithms

## Bubble sort

```go

package main

import (
	"fmt"
)

type BubbleSortUtil struct {
}

func NewBubbleSortUtil() *BubbleSortUtil {
	return &BubbleSortUtil{}
}
func (bubbleSortUtil *BubbleSortUtil) sort(integers [11]int) {
	var num int
	num = 11
	var isSwapped bool
	isSwapped = true
	
	for isSwapped {
		isSwapped = false
		var i int
		for i = 1; i < num; i++ {
			if integers[i-1] > integers[i] {
				var temp = integers[i]
				integers[i] = integers[i-1]
				integers[i-1] = temp
				isSwapped = true
			}
		}
	}
	fmt.Println(integers)
}
func main() {
	var integers [11]int = [11]int{31, 13, 12, 4, 18, 16, 7, 2, 3, 0, 10}
	var bubbleSortUtil *BubbleSortUtil
	bubbleSortUtil = NewBubbleSortUtil()
	fmt.Println("Bubble Sort Util sorting the numbers")
	bubbleSortUtil.sort(integers)
}
```


```shell

go run bubble_sort_util.go
```

# REST API in Go
You can use `Fiber` to design and develop REST API in Go Lang. 
Fiber is a REST API based open-source framework.  
It was built along on the lines of Express.js with Fasthttp.

```go

package main

import (
	"log"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
)

func main() {
	RESTapi := fiber.New()

	RESTapi.Use(cors.New())

	customers := RESTapi.Group("/customers")

	customers.Get("/", func(context *fiber.Ctx) error {
		return context.SendString("Customers are being loaded")
	})

	log.Fatal(RESTapi.Listen(":5000"))
}
```

```shell

go mod init customers_api
go mod tidy

go build 

./customers_api
```

http://127.0.0.1:5000/customers.

# REST API interacting with relational database
- use `Gin` REST framework. based on HTTP Router {routing and form validation}. 
- use `GORM` to persist data to the relational database.

entry file `main.go` invokes the code in controllers and model modules. 
The `models/` have the database interaction code. 
The `controllers/` have the routing code in Go Lang.

create an object model in Go Lang based on a relational database model. 
In the `models/` package, we create an `employee` object, which is mapped by _GORM_ to a relational database table. 
The object fields are mapped to relational database column types:

`models.go`
```go

package models
import (
"gorm.io/driver/sqlite"
"gorm.io/gorm"
)

type Employee struct {
	ID          uint `json:"id" gorm:"primary_key"`
	Name        string `json:"name"`
	Department  string `json:"department"`
}

var DB *gorm.DB

func ConnectDatabase() {
	database, error := gorm.Open(sqlite.Open("customers.db"), &gorm.Config{})
	if error != nil {
		panic("Failed to connect to database!")
	}
	error = database.AutoMigrate(&Employee{})
	if error != nil {
		return
	}
	DB = database
}
```

In the controllers package, different routes are handled to query to update the database:
`controllers.go`

```go

package controllers

import (
	"net/http"
	"github.com/gin-gonic/gin"
	"github.com/rahmanfadhil/gin-bookstore/models"
)

type CreateEmployeeInput struct {
	Name       string `json:"name" binding:"required"`
	Department string `json:"department" binding:"required"`
}

type UpdateEmployeeInput struct {
	Name       string `json:"name"`
	Department string `json:"department"`
}

func FindEmployees(context *gin.Context) {
	var employees []models.Employee
	models.DB.Find(&employees)
	context.JSON(http.StatusOK, gin.H{"data": employees})
}

func CreateEmployee(context *gin.Context) {
	var input CreateEmployeeInput
	if err := context.ShouldBindJSON(&input); err != nil {
		context.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	employee := models.Employee{Name: input.Name, Department: input.Department}
	models.DB.Create(&employee)
	context.JSON(http.StatusOK, gin.H{"data": employee})
}

func FindEmployee(context *gin.Context) {

	var employee models.Employee

	if err := models.DB.Where("id = ?", context.Param("id")).First(&employee).Error; err != nil {
		context.JSON(http.StatusBadRequest, gin.H{"error": "Record not found!"})
		return
	}
    context.JSON(http.StatusOK, gin.H{"data": employee})
}

func UpdateEmployee(context *gin.Context) {

	var employee models.Employee
    if err:= models.DB.Where("id = ?", context.Param("id")).First(&employee).Error; err != nil {
		context.JSON(http.StatusBadRequest, gin.H{"error": "Record not found!"})
		return
	}
    
	var input UpdateEmployeeInput
	if err:= context.ShouldBindJSON(&input); err != nil {
		context.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	models.DB.Model(&employee).Updates(input)
	context.JSON(http.StatusOK, gin.H{"data": employee})
}

func DeleteEmployee(context *gin.Context) {
    var employee models.Employee
	if err:= models.DB.Where("id = ?", context.Param("id")).First(&employee).Error; err != nil {
		context.JSON(http.StatusBadRequest, gin.H{"error": "Record not found!"})
		return
	}

	models.DB.Delete(&employee)
	context.JSON(http.StatusOK, gin.H{"data": true})
}
```



In the main.go, different Gin routes are mapped to controller functions defined in Go Lang:

main.go
```go

package main

import (
	"github.com/gin-gonic/gin"
	"REST_api_relational_db/controllers"
	"REST_api_relational_db/models"
)

func main() {
	gin_instance := gin.Default()
	models.ConnectDatabase()

	gin_instance.GET("/employees",		controllers.FindEmployees)
	gin_instance.POST("/employees",		controllers.CreateEmployee)
	gin_instance.GET("/employees/:id",		controllers.FindEmployee)
	gin_instance.PATCH("/employees/:id",		controllers.UpdateEmployee)
	gin_instance.DELETE("/employees/:id",		controllers.DeleteEmployee)
	
	err := gin_instance.Run()
	
	if err != nil {
		return
	}
}
```

You can now compile and run the main.go. 
The command to create a module REST_api_relational_db is:
```shell
go mod init REST_api_relational_db
go mod tidy

go build
```

# REST API interacting with No SQL database (MongoDB)
MongoDB driver `go.mongodb.org/mongo-driver/mongo` package:
1. creating REST API and routes. main.go will have the routes set through package routes.
2. code for REST API using Fiber and MongoDB.


main.go
```go

package main

import (
	"github.com/gofiber/fiber/v2"
	"REST_api_nosql_db/routes"
)

func main() {
	app := fiber.New()
	routes.SupplierRoute(app)
	
	app.Listen(":6000")
}
```
3. the routes, supplierRoute has Create/Get/Edit/Delete/GetAll supplier routes configured through Post/Get/Delete methods. 

        Get    /supplier/:supplierid        Get Supplier
        Post  /supplier/:supplierid         CreateSupplier
        Put      /supplier/:supplierid      EditSupplier
        Delete  /supplier/:supplierid       DeleteSupplier
        Get      /suppliers                 GetAllSuppliers

routes.go
```go

package routes
import (
	"github.com/gofiber/fiber/v2"
	"REST_api_nosql_db/controllers"
)

func SupplierRoute(app *fiber.App) {
	app.Post("/supplier", controllers.CreateSupplier)
	app.Get("/supplier/:supplierId", controllers.GetSupplier)
	app.Put("/supplier/:supplierId", controllers.EditSupplier)
	app.Delete("/supplier/:supplierId", controllers.DeleteSupplier)
	app.Get("/suppliers", controllers.GetAllSuppliers)
}
```
4. The controller has the routes mapped to methods that talk to MongoDB. Methods are used to query and update the Mongo database:
controllers.go

```go

package controllers

import (
	"context"
	"net/http"
	"REST_api_nosql_db/configs"
	"REST_api_nosql_db/models"
	"REST_api_nosql_db/responses"
	"time"
	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var supplierCollection *mongo.Collection = configs.GetCollection(configs.DB, "suppliers")

var validate = validator.New()

func CreateSupplier(c *fiber.Ctx) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	var supplier models.Supplier
	defer cancel()
	if err := c.BodyParser(&supplier); err != nil {
		return c.Status(http.StatusBadRequest).JSON(responses.SupplierResponse{Status: http.StatusBadRequest, Message: "error", Data: &fiber.Map{"data": err.Error()}})
	}
	if validationErr := validate.Struct(&supplier); validationErr != nil {
		return c.Status(http.StatusBadRequest).JSON(responses.SupplierResponse{Status: http.StatusBadRequest, Message: "error", Data: &fiber.Map{"data": validationErr.Error()}})
	}
	newSupplier := models.Supplier{
		Id:      primitive.NewObjectID(),
		Name:    supplier.Name,
		Address: supplier.Address,
		Mobile:  supplier.Mobile,
	}
	result, err := supplierCollection.InsertOne(ctx, newSupplier)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error",
			Data: &fiber.Map{"data": err.Error()}})
	}
	return c.Status(http.StatusCreated).JSON(responses.SupplierResponse{Status: http.StatusCreated, Message: "success", Data: &fiber.Map{"data": result}})
}

func GetSupplier(c *fiber.Ctx) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	supplierId := c.Params("supplierId")
	var supplier models.Supplier
	defer cancel()
	objId, _ := primitive.ObjectIDFromHex(supplierId)
	err := supplierCollection.FindOne(ctx, bson.M{"id": objId}).Decode(&supplier)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error", Data: &fiber.Map{"data": err.Error()}})
	}
	return c.Status(http.StatusOK).JSON(responses.SupplierResponse{Status: http.StatusOK, Message: "success", Data: &fiber.Map{"data": supplier}})
}

func EditSupplier(c *fiber.Ctx) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	supplierId := c.Params("supplierId")
	var supplier models.Supplier
	defer cancel()
	objId, _ := primitive.ObjectIDFromHex(supplierId)
	if err := c.BodyParser(&supplier); err != nil {
		return c.Status(http.StatusBadRequest).JSON(responses.SupplierResponse{Status: http.StatusBadRequest, Message: "error", Data: &fiber.Map{"data": err.Error()}})
	}
	if validationErr := validate.Struct(&supplier); validationErr != nil {
		return c.Status(http.StatusBadRequest).JSON(responses.SupplierResponse{Status: http.StatusBadRequest, Message: "error", Data: &fiber.Map{"data": validationErr.Error()}})
	}
	update := bson.M{"name": supplier.Name, "address": supplier.Address, "mobile": supplier.Mobile}
	result, err :=
		supplierCollection.UpdateOne(ctx, bson.M{"id": objId}, bson.M{"$set": update})
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error", Data: &fiber.Map{"data": err.Error()}})
	}
	var updatedSupplier models.Supplier
	if result.MatchedCount == 1 {
		err := supplierCollection.FindOne(ctx, bson.M{"id": objId}).Decode(&updatedSupplier)
		if err != nil {
			return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error", Data: &fiber.Map{"data": err.Error()}})
		}
	}
	return c.Status(http.StatusOK).JSON(responses.SupplierResponse{Status: http.StatusOK, Message: "success", Data: &fiber.Map{"data": updatedSupplier}})
}

func DeleteSupplier(c *fiber.Ctx) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	supplierId := c.Params("supplierId")
	defer cancel()
	objId, _ :=
		primitive.ObjectIDFromHex(supplierId)
	result, err :=
		supplierCollection.DeleteOne(ctx, bson.M{"id": objId})
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error", Data: &fiber.Map{"data": err.Error()}})
	}
	if result.DeletedCount < 1 {
		return c.Status(http.StatusNotFound).JSON(
			responses.SupplierResponse{Status: http.StatusNotFound, Message: "error", Data: &fiber.Map{"data": "This SupplierID not found, !"}},
		)
	},
	return c.Status(http.StatusOK).JSON(
		responses.SupplierResponse{Status: http.StatusOK, Message: "success", Data: &fiber.Map{"data": "Supplier  deleted!"}},
	)
}

func GetAllSuppliers(c *fiber.Ctx) error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	var suppliers []models.Supplier
	defer cancel()
	results, err := supplierCollection.Find(ctx, bson.M{})
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error", Data: &fiber.Map{"data": err.Error()}})
	}
	defer results.Close(ctx)
	for results.Next(ctx) {
		var singleSupplier models.Supplier
		if err = results.Decode(&singleSupplier); err != nil {
			return c.Status(http.StatusInternalServerError).JSON(responses.SupplierResponse{Status: http.StatusInternalServerError, Message: "error", Data: &fiber.Map{"data": err.Error()}})
		}
		suppliers = append(suppliers, singleSupplier)
	}
	return c.Status(http.StatusOK).JSON(
		responses.SupplierResponse{Status: http.StatusOK, Message: "success", Data: &fiber.Map{"data": suppliers}},
	)
}

```
5. Models have the Supplier struct information. Supplier has Id, Name, Address, and Mobile:
models.go

```go

package models

import "go.mongodb.org/mongo-driver/bson/primitive"

type Supplier struct {
	Id      primitive.ObjectID `json:"id,omitempty"`
	Name    string             `json:"name,omitempty" validate:"required"`
	Address string             `json:"address,omitempty" validate:"required"`
	Mobile  string             `json:"mobile,omitempty" validate:"required"`
}

```
6. Setup.go configuration for MongoDB client initiation and getting collection method, which retrieves the data from the No SQL database:
Setup.go
```go

package configs

import (
"context"
"fmt"
"go.mongodb.org/mongo-driver/mongo"
"go.mongodb.org/mongo-driver/mongo/options"
"log"
"time"
)
func ConnectDB() *mongo.Client {
	client, err := mongo.NewClient(options.Client().ApplyURI(EnvMongoURI()))
	fmt.Println("client ")
	if err != nil {
		log.Fatal(err)
	}
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)
	err = client.Connect(ctx)
	if err != nil {
		log.Fatal(err)
	}
	err = client.Ping(ctx, nil)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Println("Connected to MongoDB")
	return client
}

var DB *mongo.Client = ConnectDB()

func GetCollection(client *mongo.Client, collectionName string) *mongo.Collection {
	collection := client.Database("golangAPI").Collection(collection Name)
	return collection
}
```
7. Env.go has the environment information, which will be loaded from .env (hidden file).


env.go
```go

package configs

import (
	"fmt"
	"github.com/joho/godotenv"
	"log"
	"os"
)

func EnvMongoURI() string {
	fmt.Println("Getting from Env")
	err := godotenv.Load()
	if err != nil {
		log.Fatal(".env file could not be loaded")
	}
	return os.Getenv("MONGOURI")
}

```
.env will have the Mongo URI address to be connected. This MongoDB datasource is configured on atlas Mongo Cloud database.

https://cloud.mongodb.com/
.env
```dotenv

MONGOURI=mongodb+srv://Cluster72877:<EMAIL>/?ssl=true&retryWrites=true
```
8. You can now compile and run the main.go. The command to create a module REST_api_nosql_db is:
```shell

go mod init REST_api_nosql_db
go mod tidy

go build
```
The output will be as follows:

The output shows how the REST API server is initialized. The following are the screenshots for the different routes:
Checking server with GET Route for the server healthcheck.

# REST API interacting with message queues
In this section, we will look at how to develop a REST API that can interact with message queues. We will use Gin and Rabbit MQ.

To start with, let us look at the code project structure:

The code project has three high-level modules—consumer, producer, and rabbit-mq. The consumer module has sub modules —environment, handlers, and utils. The producer module has config, controllers, environment, middlewares, models, routers, and utils modules. The rabbit-mq module has the configuration
for rabbitmq:
1. Let us look at the code for developing REST API using Gin and RabbitMQ:
main.go
```go

package main
import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"REST_api_mq/producer/config"
	"REST_api_mq/producer/utils"
)

func init() {
	mode := utils.GetEnvVar("GIN_MODE")
	gin.SetMode(mode)
}
func main() {
	appGin := config.CreateApp()
	addrGin := utils.GetEnvVar("GIN_ADDR")
	portGin := utils.GetEnvVar("GIN_PORT")
	log.Info().Msgf("App is up at http//:%s:%s", addrGin, portGin)
	if error := appGin.Run(fmt.Sprintf("%s:%s", addrGn, portGin));
		error != nil {
		log.Fatal().Err(error).Msg("Http Server setup failed")
	}
}
```
2. Now, let us look at the createApp to see how the routes are set through routers:
Creating_app.go
```go

package config

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"REST_api_mq/producer/middlewares"
	"REST_api_mq/producer/routers"
)

func CreateApp() *gin.Engine {
	log.Info().Msg("service starting")
	app := gin.New()
	app.Use(gin.Recovery())
	app.SetTrustedProxies(nil)
	log.Info().Msg(" cors, request id, request logging middleware added")
	app.Use(middlewares.CORSMiddleware(), middlewares.RequestID(), middlewares.RequestLogger())
	log.Info().Msg("routers setup")
	routers.SetupRouters(app)
	return app
}

```
3. The Routers package has the routes mapped to methods that talk to the message queue:
```
Get   /ping     controllers.ping
Post /publish/example controllers.Example
```
setup.go
```go

package routers

import (
	"github.com/gin-gonic/gin"
	"REST_api_mq/producer/controllers"
)

func CreateRouters(engine *gin.Engine) {
	version1 := engine.Group("/v1")
	{
		version1.GET("/ping", controllers.Ping)
		version1.POST("/publish/example", controllers.Example)
	}
}
```
4. The controllers package has the message publisher configured to publish messages to a message queue.

controllers.go
```go

package controllers
import (
	"net/http"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"REST_api_mq/producer/environment"
	"REST_api_mq/producer/models"
	"REST_api_mq/producer/utils"
)

func Example(context *gin.Context) {
	var msg models.Message
	request_id := context.GetString("x-request-id")
	if binderr :=
		context.ShouldBindJSON(&msg); binderr != nil {
		log.Error().Err(binderr).Str("request_id", request_id).Msg("Error occurred while binding request data")
		context.JSON(http.StatusUnprocessableEntity,
			gin.H{
				"message": binderr.Error(),
			})
		return
	}
	connectionString := utils.GetEnvVar("RMQ_URL")
	producer := utils.MessagePublisher{
		environment.EXAMPLE_QUEUE,
		connectionString,
	}
	producer.PublishMessage("text/plain",
		[]byte(msg.Message))
	context.JSON(http.StatusOK, gin.H{
		"response": "Message received from REST API",
	})
}
```
5. MessagePublisher is a struct defined in the utils package. It has a method to publish messages to a queue defined in the environment:
Publisher.go
```go

package utils

import (
	"github.com/rs/zerolog/log"
	"github.com/streadway/amqp"
)

type MessagePublisher struct {
	Queue            string
	ConnectionString string
}

func (x MessagePublisher) OnError(err error, msg string) {
	if err != nil {
		log.Err(err).Msgf("Publishing message error '%s' queue. Error message: %s",
			publisher.Queue, msg)
	}
}
func (publisher MessagePublisher) PublishMessage(contentType string, body []byte) {
	conn, error := amqp.Dial(publisher.ConnectionString)
	publisher.OnError(error, "RabbitMQ not connected")
	defer conn.Close()
	channel, err := conn.Channel()
	publisher.OnError(err, "Channel not opened")
	defer channel.Close()
	q, error := channel.QueueDeclare(publisher.Queue,
		false,
		false,
		false,
		false,
		nil,
	)
	publisher.OnError(error, "Queue Not declared")
	error = channel.Publish(
		"",
		q.Name,
		false,
		false,
		amqp.Publishing{
			ContentType: contentType,
			Body:        body,
		})
	publisher.OnError(error, "message not published")
}
```
6. Now, let us look at the consumer package:
main.go
```go

package main

import (
	"REST_api_mq/consumer/environment"
	"REST_api_mq/consumer/handlers"
	"REST_api_mq/consumer/utils"
)

func main() {
	connectionString := utils.GetEnvVar("RMQ_URL")
	messageQueue := utils.MessageConsumer{
		enviornment.EXAMPLE_QUEUE,
		connectionString,
		handlers.HandleMessaage,
	}
	forever := make(chan bool)
	go messageQueue.Consume()
	<-forever
}

```
7. MessageConsumer is created from the utils package:
Message_consumer.go

```go

package utils

import (
	"github.com/rs/zerolog/log"
	"github.com/streadway/amqp"
)

type MessageConsumer struct {
	Queue            string
	ConnectionString string
	MsgHandler       func(queue string, msg amqp.Delivery, err error)
}

func (consumer MessageConsumer) OnError(errors error, msg string) {
	if errors != nil {
		consumer.MsgHandler(consumer.Queue, amqp.Delivery{}, errors)
	}
}
func (consumer MessageConsumer) Consume() {
	conn, err := amqp.Dial(consumer.ConnectionString)
	consumer.OnError(err, "Failed to connect to RabbitMQ")
	defer conn.Close()
	channel, err := conn.Channel()
	consumer.OnError(err, "Failed to open a channel")
	defer channel.Close()
	q, err := channel.QueueDeclare(consumer.Queue, false, false, false, false, nil)
	consumer.OnError(err, "Failed to declare a queue")
	msgs, err := channel.Consume(q.Name, "", true, false, false, false, nil)
	consumer.OnError(err, "Failed to register a consumer")
	forever := make(chan bool)
	go func() {
		for delivery := range msgs {
			consumer.MsgHandler(consumer.Queue, delivery, nil)
		}
	}()
	log.Info().Msgf("Started listening-messages from '%s' queue", consumer.Queue)
	<-forever
}

```
8. The environment package has the constants defined for the environment file, directory, and message queue:
constants.go
```go

package environment

const ENV_FILE = ".env"
const ENV_FILE_DIRECTORY = "."
const EXAMPLE_QUEUE = "message_queue"
```
9. .env will Rabbit message queue configuration URL and Log level:
.env
```dotenv

LOG_LEVEL = debug
RMQ_URL = amqp://guest:guest@localhost:5000/
```
10. compile and run the rabbitmq, producer, and consumer services in the docker

docker-compose.yaml:
```yaml
version: '3.6'

networks:
  rabbitmq-example:
    driver: bridge

services:
  rabbitmq:
    image: 'rabbitmq:3-management'
    networks:
      - rabbitmq-example
    volumes:
      - ./rabbit-mq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro 
    ports:
      - "8080:15672"
    healthcheck:
      test: [ "CMD", "rabbitmqctl", "status"]
      interval: 5s
      timeout: 15s
      retries: 5

  producer:
    build: ./producer
    ports:
      - "5050:5050"
    networks:
      - rabbitmq-example
    depends_on:
      - rabbitmq
    environment:
      GIN_MODE: "release"
      GIN_HTTPS: "false"
      GIN_ADDR: "0.0.0.0"
      GIN_PORT: "5050"
      LOG_LEVEL: "debug"
      RMQ_URL: "amqp://guest:guest@rabbitmq:5673/"

  consumer:
    build: ./consumer
    networks:
      - rabbitmq-example
    depends_on:
      - rabbitmq
    RESTart: on-failure
    environment:
      LOG_LEVEL: "debug"
      RMQ_URL: "amqp://guest:guest@rabbitmq:5673/"
```
The yaml file has three services configured—rabbitmq, producer, and consumer. Dependencies are configured at each service level. The consumer service depends on rabbitmq service. Similarly producer service depends on rabbitmq service.

The command to run the docker is: 
```shell

docker compose up
```
The output will be as follows:

The /publish endpoint is invoked using the POST method in the postman. The output of the response is as follows:

In the above screenshot, REST API with the POST method is used
to send the message to the producer configured to listen to the
message queue. The consumer consumes the message and sends
the response back.


# Go best practices
- Always mention the datatype of the variables which you are creating.
- Do not have an import or variable which you are not going to use.
- Always follow coding conventions and guidelines.
- Errors always need to be handled. 
- Reuse code by having a utils package.

A switch can be used for handling different cases.
- Always organize the code into packages, structs, and functions.
- Go modules can be used to build components and modules.
- Go proxy can be used to have availability and immutability. 
- You can use the channel with a timeout interval.
- You can use context instead of channel.
- Always login with the line number.
- You can use `panic`, `defer`, and `recover` to handle complex errors.


# Conclusion
Examples were presented to demonstrate how REST API can interact with relational databases, No SQL databases, and message queues using Go Lang.

REST API can be built using Go Lang using Fiber and Gin packages.

Relational databases can be queried using object-relational mapping tools like GORM, and GORM has drivers for SQLite.

REST API interacting with No SQL databases can be developed using Fiber and MongoDB. The Mongo driver package can be used for querying the Mongo database in
the Go language.

REST API interacting with message queues can be developed using Rabbit MQ and Fiber.


