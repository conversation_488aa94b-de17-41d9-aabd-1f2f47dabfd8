package main

import (
	"os"
	"testing"
)

// ensure that we get 52 cards when we run newDeck
func TestNewDeck(t *testing.T) {
	d := newDeck()

	if len(d) != 52 {
		t.<PERSON><PERSON><PERSON>("Expected deck length of 52, but got %v", len(d))
	}

	if d[0].value != "A" || d[0].suit != "♠️" {
		t.<PERSON><PERSON>("Expected first card of A ♠️, but got %v", d[0])
	}

	if d[len(d)-1].value != "K" || d[len(d)-1].suit != "♣️" {
		t.<PERSON><PERSON><PERSON>("Expected last card of K ♣️, but got %v", d[len(d)-1])
	}
}

func TestDeal(t *testing.T) {
	d := newDeck()
	hand, _ := deal(d, 5)

	if len(hand) != 5 {
		t.<PERSON><PERSON><PERSON>("Expected hand length of 5, but got %v", len(hand))
	}

	if hand[0].value != "A" || hand[0].suit != "♠️" {
		t.<PERSON><PERSON><PERSON>("Expected first card of A ♠️, but got %v", hand[0])
	}

	if hand[len(hand)-1].value != "5" || hand[len(hand)-1].suit != "♠️" {
		t.<PERSON><PERSON><PERSON>("Expected last card of 5 ♠️, but got %v", hand[len(hand)-1])
	}
}

func TestSaveToFile(t *testing.T) {
	d := newDeck()
	d.saveToFile("_test.txt")

	loadedDeck := newDeckFromFile("_test.txt")
	if len(loadedDeck) != 52 {
		t.Errorf("Expected deck length of 52, but got %v", len(loadedDeck))
	}

	os.Remove("_test.txt")
}

func TestSaveToFileAndLoadFromFile(t *testing.T) {
	d := newDeck()
	d.saveToFile("_test.txt")

	loadedDeck := newDeckFromFile("_test.txt")
	if len(loadedDeck) != 52 {
		t.Errorf("Expected deck length of 52, but got %v", len(loadedDeck))
	}

	os.Remove("_test.txt")
}

func TestShuffle(t *testing.T) {
	d := newDeck()
	d.shuffle()

	if d[0].value == "A" && d[0].suit == "♠️" {
		t.Errorf("Expected first card to be shuffled, but got %v", d[0])
	}

	if d[len(d)-1].value == "K" && d[len(d)-1].suit == "♣️" {
		t.Errorf("Expected last card to be shuffled, but got %v", d[len(d)-1])
	}
}
