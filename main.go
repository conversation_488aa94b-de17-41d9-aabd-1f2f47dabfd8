package main

import (
	"fmt"
	"net/http"
	"time"
)

type NetworkErr struct {
	message string
}

func (e NetworkErr) Error() string {
	return e.message
}

func checkStatus(url string, c chan string) (int, error) {
	fmt.Println("Checking", url)
	resp, err := http.Get(url)
	if err != nil {
		c <- url
		return resp.StatusCode, err
	}

	if resp.StatusCode != 200 {
		c <- url
		return resp.StatusCode, nil
	}

	return resp.StatusCode, NetworkErr{message: "status code is not OK"}
}

// get optional filename from command line
func main() {

	links := []string{
		"https://www.yahoo.com",
		"https://www.google.com",
		"https://www.bing.com",
		"https://www.duckduckgo.com",
		"https://www.ask.com",
	}

	c := make(chan string)

	for _, link := range links {
		go checkStatus(link, c)
	}

	for l := range c {
		go func(link string) {
			fmt.Println("waiting 2 seconds before checking ", link)
			time.Sleep(time.Second * 2)
			fmt.Println("Checking", link)
			statusCode, err := checkStatus(link, c)
			if err != nil {
				fmt.Println(link, "Error:", err)
			} else {
				fmt.Println(link, "Status code:", statusCode)
			}

		}(l)
	}

	//
	//resp, err := http.Get("https://www.cnn.com")
	//
	//if err != nil {
	//	fmt.Println("Error:", err)
	//	os.Exit(1)
	//}

	//fmt.Println(resp.Status)
	//
	//if resp.StatusCode != 200 {
	//	fmt.Println("Status code error:", resp.StatusCode)
	//	os.Exit(1)
	//}
	//
	////fmt.Printf("%+v", resp.Header)
	//
	//// read the body
	//bytes, err := io.ReadAll(resp.Body)
	//
	//if err != nil {
	//	fmt.Println("Error:", err)
	//	os.Exit(1)
	//}
	//
	////fmt.Println(string(bytes))
	//
	//// save to file
	//_ = os.WriteFile("cnn.html", bytes, 0666)
	//
	//os.Exit(0)
	//
	//var d deck
	//
	////var card string = "Ace of Spades"
	//args := os.Args[1:]
	//if len(args) > 0 {
	//	filename := args[0]
	//	fmt.Println("Loading deck from", filename)
	//	d = newDeckFromFile(filename)
	//} else {
	//	fmt.Println("Creating new deck")
	//
	//	d = newDeck()
	//}
	//
	//// random seed
	//fmt.Println("Shuffling deck")
	//d.shuffle()
	//hand, _ := deal(d, 5)
	//
	//fmt.Println("Hand:")
	//hand.print()
	////fmt.Println("Remaining Cards:")
	////remainingCards.print()
	//
	//d.saveToFile("my_cards.txt")
	//
	//fmt.Println(groupBySuit(d))
}
