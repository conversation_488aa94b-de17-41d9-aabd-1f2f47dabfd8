package main

import (
	"fmt"
	"math/rand"
	"os"
	"strings"
	"time"
)

type card struct {
	value string
	suit  string
}

func fromString(s string) card {
	parts := strings.Split(s, " ")
	return card{value: parts[0], suit: parts[1]}
}

func (c card) String() string {
	return c.value + " " + c.suit
}

type deck []card

func (d deck) print() {
	for _, card := range d {
		fmt.Println(card)
	}
}

func newDeck() deck {
	cards := deck{}

	cardSuits := []string{"♠️", "🔹", "❤️", "♣️"}
	cardValues := []string{"A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"}

	for _, suit := range cardSuits {
		for _, value := range cardValues {
			cards = append(cards, card{value: value, suit: suit})
		}
	}

	return cards
}

func deal(d deck, handSize int) (deck, deck) {
	return d[:handSize], d[handSize:]
}

func (d deck) toString(sep string) string {
	if sep == "" {
		sep = "\n"
	}

	// convert deck to []string
	s := []string{}
	for _, card := range d {
		s = append(s, card.String())
	}
	return strings.Join(s, sep)
}

func (d deck) shuffle() {
	_s := rand.NewSource(time.Now().UnixNano())
	_r := rand.New(_s)

	for i := range d {

		var newPosition int
		if i == len(d)-1 {
			newPosition = _r.Intn(len(d) - 2)

		} else {
			newPosition = _r.Intn(len(d)-i-1) + i
		}

		d[i], d[newPosition] = d[newPosition], d[i]
	}
}

func (d deck) saveToFile(filename string) {
	_ = os.WriteFile(filename, []byte(d.toString("\n")), 0666)
}

func newDeckFromFile(filename string) deck {
	bs, err := os.ReadFile(filename)
	if err != nil {
		fmt.Println("Error:", err)
		os.Exit(1)
	}

	s := strings.Split(string(bs), "\n")

	// convert []string to deck
	cards := deck{}
	for _, card := range s {
		cards = append(cards, fromString(card))
	}
	return cards
}

func groupBySuit(d deck) map[string][]card {
	// group by suit
	groups := map[string][]card{}
	for _, card := range d {
		groups[card.suit] = append(groups[card.suit], card)
	}
	return groups
}
