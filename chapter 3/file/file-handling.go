package main

import (
	"fmt"
	"log"
	"os"
)

func WriteTextFile(path string) {
	fmt.Println("Writing a Text Format file in Go lang")

	text_file, error := os.Create(path)
	if error != nil {
		log.Fatalf("could not create a text file: %s", error)
	}

	defer text_file.Close()

	length, error :=
		text_file.WriteString("This is to test writing a text formatted file" + " Code demonstrates writing and reading a text file" +
			" Go lang.")

	if error != nil {
		log.Fatalf("could not create a text file: %s", error)
	}
	fmt.Printf("Text File Name is %s\n", text_file.Name())
	fmt.Printf("Text File Length is %d bytes\n", length)
}

func FindTextFile(textFile string) {
	fmt.Println("Reading a text format  in Go lang")
	text_data, error := os.ReadFile(textFile)
	if error != nil {
		log.Panicf("cannot reading test from file: %s", error)
	}
	fmt.Printf("Text File Name: %s\n", textFile)
	fmt.Printf("Text DataSize: %d bytes\n", len(text_data))
	fmt.Printf("Text Data: %s\n", string(text_data))
}

func main() {
	path := "input_test_format.txt"
	WriteTextFile(path)
	fmt.Println()
	FindTextFile(path)
}
